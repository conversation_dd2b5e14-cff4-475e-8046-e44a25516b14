use actlogica_logs::{builder::Log<PERSON><PERSON>er, generator::generate_event_id, log_error, log_info};
use alpha_core_db::types::OrderSourceType;
use alpha_utils::constants::RedisKey;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use uuid::Uuid;

use crate::{
    log_actions::Action,
    models::{compliance::PreTradeComplianceReport, AppState, TokenClaims},
    oms::trade_compliance::pre_trade::PreTradeCompliance,
    types::ClientComputedOrders,
};

#[derive(Deserialize)]
pub struct TradeCompliance {
    pub session_id: Uuid,
}

#[derive(Serialize)]
#[serde(rename_all = "camelCase")]
pub struct TradeComplianceResponse {
    pub session_id: Uuid,
    pub summary: PreTradeComplianceReport,
    pub total_reports: usize,
    pub source_type: OrderSourceType,
}

#[tracing::instrument(fields(module = "Trade-Order-Compliance", event_id = %generate_event_id()), skip_all)]
pub async fn trade_compliance(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Query(query): Query<TradeCompliance>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: trade orders compliance",
            Action::ComputePreTrade,
        )
        .add_metadata("session_id", &query.session_id.to_string()),
    );

    let mut redis_conn = match state.tenant_redis_url.get().await {
        Ok(conn) => conn,
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("failed to get connection"),
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    let key = format!("{}:{}", RedisKey::ClientComputedOrders, query.session_id);

    let order_string: Vec<String> = redis_conn.zrange(key, 0, -1).await.map_err(|e| {
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("failed to get orders"),
        });
        log_error(
            LogBuilder::system("Failed to get orders")
                .add_metadata("session_id", &query.session_id.to_string())
                .add_metadata("requested_by", user_name)
                .add_metadata("error", &e.to_string()),
        );
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response));
    })?;

    let order_source_key = format!("{}:{}", RedisKey::PreTradeSessionOrderSource, query.session_id);

    let order_source: OrderSourceType = redis_conn.get(order_source_key).await.map_err(|e| {
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("failed to get order source"),
        });
        log_error(
            LogBuilder::system("Failed to get order source")
                .add_metadata("session_id", &query.session_id.to_string())
                .add_metadata("requested_by", user_name)
                .add_metadata("error", &e.to_string()),
        );
        return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response));
    })?;

    let orders: Vec<ClientComputedOrders> = order_string
        .into_iter()
        .map(|s| {
            serde_json::from_str::<ClientComputedOrders>(&s).map_err(|e| {
                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("failed to deserialise orders"),
                });
                log_error(
                    LogBuilder::system("Failed to deserialise orders")
                        .add_metadata("session_id", &query.session_id.to_string())
                        .add_metadata("requested_by", user_name)
                        .add_metadata("error", &e.to_string()),
                );
                return (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response));
            })
        })
        .collect::<Result<_, _>>()?;

    let pretrade_compliance = PreTradeCompliance {
        app_state: state.clone(),
        session_id: query.session_id,
    };

    let (compliance_report, total_reports) = match pretrade_compliance.compute_compliance_for_orders(&orders).await {
        Ok(report) => report,
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("{}",err),
            });
            log_error(
                LogBuilder::system("Failed to get orders")
                    .add_metadata("session_id", &query.session_id.to_string())
                    .add_metadata("requested_by", user_name)
                    .add_metadata("error", &err.to_string()),
            );
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    log_info(
        LogBuilder::system("RES: Compute Pre-trade Order successfully")
            .add_metadata("session_id", &query.session_id.to_string())
            .add_metadata("created_by", user_name),
    );
    Ok((
        StatusCode::ACCEPTED,
        Json(TradeComplianceResponse {
            session_id: query.session_id,
            source_type: order_source,
            summary: PreTradeComplianceReport {
                portfolio_reports: compliance_report,
            },
            total_reports,
        }),
    ))
}
