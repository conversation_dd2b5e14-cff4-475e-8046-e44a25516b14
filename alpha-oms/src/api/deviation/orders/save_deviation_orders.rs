use crate::{api::deviation::utils::get_deviation_portfolios, log_actions::Action};
use actlogica_logs::{builder::Log<PERSON>uilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    oms::OrderComputer,
    types::CustomisedOrder,
};

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveDeviationOrderPayload {
    pub deviation_id: String,
}

#[tracing::instrument(fields(module = "Deviation-analysis", event_id = %generate_event_id()), skip_all)]
pub async fn save_deviation_orders(
    State(state): State<Arc<AppState>>,
    Extension(tenant): Extension<TokenClaims>,
    Json(payload): Json<SaveDeviationOrderPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;
    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Save Deviation Orders",
            Action::SaveDeviationOrder,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.deviation_id),
    );

    let computer = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        state.master_redis_url.clone(),
        tenant.name.clone(),
        0f64,
    );

    let key = format!(
        "{}:{}",
        RedisKey::DeviationOrders.to_string(),
        payload.deviation_id.clone()
    );

    let mut redis_conn = match state.tenant_redis_url.get().await {
        Ok(conn) => conn,
        Err(err) => {
            log_error(LogBuilder::system(
                format!("Failed: Redis connection: {:?}", err).as_str(),
            ));
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "status": "error",
                    "message": "Failed to get Redis connection"
                })),
            ));
        }
    };

    // Get all elements from the sorted set (0 to -1 means all elements)
    let results: Vec<Vec<u8>> = match redis_conn.zrange(key, 0, -1).await {
        Ok(results) => results,
        Err(err) => {
            log_error(
                LogBuilder::system(format!("Failed: Fetching orders from Redis: {:?}", err).as_str())
                    .add_metadata("deviation_id", &payload.deviation_id),
            );
            return Err((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "status": "error",
                    "message": "Failed to retrieve orders from Redis"
                })),
            ));
        }
    };

    // Deserialize each item back to DeviationReport
    let orders: Vec<CustomisedOrder> = results
        .into_iter()
        .filter_map(|bytes| serde_json::from_slice::<CustomisedOrder>(&bytes).ok())
        .collect();

    let invalid_orders = crate::oms::deviation::validate_deviation_orders::validate_deviation_orders(orders.clone());

    if invalid_orders.len() > 0 {
        log_error(LogBuilder::system("Invalid Orders").add_metadata("deviation_id", &payload.deviation_id));

        return Err((
            StatusCode::BAD_REQUEST,
            Json(json!({
                "status": "error",
                "message": "Please Validate all the Orders"
            })),
        ));
    }

    //Now get all portfolios for these orders
    let portfolio_ids = orders.iter().map(|f| f.portfolio_id.clone()).collect::<Vec<String>>();

    //This will return error if it didn't find details for atleast one of the portfolio provided
    let portfolios = match get_deviation_portfolios(&mut redis_conn, &portfolio_ids).await {
        Ok(res) => res,
        Err(err) => {
            log_error(
                LogBuilder::system(format!("Failed: No portfolio found: {:?}", err).as_str())
                    .add_metadata("deviation_id", &payload.deviation_id),
            );
            return Err((
                StatusCode::BAD_REQUEST,
                Json(json!({
                    "status": "error",
                    "message": err.to_string()
                })),
            ));
        }
    };

    let result = computer
        .compute_order_for_custom_trade_for_deviation(orders, portfolios)
        .await
        .map_err(|err| {
            log_error(
                LogBuilder::system(
                    format!(
                        "Failed: Compute order for custom trade for deviation. REASON: {:?}",
                        err
                    )
                    .as_str(),
                )
                .add_metadata("deviation_id", &payload.deviation_id),
            );
            (
                StatusCode::BAD_REQUEST,
                Json(json!({
                    "status": "error",
                    "message": "Failed to Compute"
                })),
            )
        })?;

    let mut conn = state.db.get().await.map_err(|err| {
        log_error(LogBuilder::system(format!("Failed: DB connection: {:?}", err).as_str()));
        (
            StatusCode::BAD_REQUEST,
            Json(json!({
                "status": "error",
                "message": "Failed to get database connection"
            })),
        )
    })?;

    let save_order =
        crate::oms::deviation::deviation_compute_order::save_deviation_orders(&mut conn, result, tenant.name.clone())
            .await;

    match save_order {
        Ok(()) => {
            log_info(
                LogBuilder::system("RES: Successfully Saved Deviation Orders")
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_name", &user_name)
                    .add_metadata("deviation_id", &payload.deviation_id),
            );
            Ok::<(StatusCode, Json<Value>), (StatusCode, Json<Value>)>((
                StatusCode::ACCEPTED,
                Json(json!({
                    "status": "success",
                    "message": "Saved Successfully"
                })),
            ))
        }
        Err(msg) => {
            log_error(
                LogBuilder::system(format!("Failed: Saving Deviation Orders: {:?}", msg).as_str())
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_name", &user_name)
                    .add_metadata("deviation_id", &payload.deviation_id),
            );
            Err::<(StatusCode, Json<Value>), (StatusCode, Json<Value>)>((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(json!({
                    "status": "failed",
                    "message": msg
                })),
            ))
        }
    }
}
