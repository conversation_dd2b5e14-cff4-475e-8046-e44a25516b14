use alpha_core_db::tenant_clickhouse::reconciliation::recon_result::{MatchStatus, ReconResult};
use chrono::NaiveDate;
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Debug, ToSchema, Clone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct ReconRunResultRsp {
    pub id: String,
    pub run_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub holding_date: NaiveDate,
    pub client_code: String,
    pub name: String,
    pub symbol: String,
    pub security_name: String,
    pub isin: String,
    pub client_holdings: f64,
    pub alpha_holdings: f64,
    pub holding_diff: f64,
    pub client_unit_cost: f64,
    pub alpha_unit_cost: f64,
    pub unit_cost_diff: f64,
    pub client_total_cost: f64,
    pub alpha_total_cost: f64,
    pub total_cost_diff: f64,
    pub client_market_value: f64,
    pub alpha_market_value: f64,
    pub market_value_diff: f64,
    pub client_accrued_income: f64,
    pub alpha_accrued_income: f64,
    pub accrued_income_diff: f64,
    pub match_status: String,
    pub unmatched_count: i32,
}

impl From<ReconResult> for ReconRunResultRsp {
    fn from(r: ReconResult) -> Self {
        ReconRunResultRsp {
            id: r.id,
            run_id: r.run_id,
            holding_date: r.holding_date,
            client_code: r.client_code,
            name: r.name,
            symbol: r.symbol,
            security_name: r.security_name,
            isin: r.isin,
            client_holdings: r.client_holdings,
            alpha_holdings: r.alpha_holdings,
            holding_diff: r.holding_diff,
            client_unit_cost: r.client_unit_cost,
            alpha_unit_cost: r.alpha_unit_cost,
            unit_cost_diff: r.unit_cost_diff,
            client_total_cost: r.client_total_cost,
            alpha_total_cost: r.alpha_total_cost,
            total_cost_diff: r.total_cost_diff,
            client_market_value: r.client_market_value,
            alpha_market_value: r.alpha_market_value,
            market_value_diff: r.market_value_diff,
            client_accrued_income: r.client_accrued_income,
            alpha_accrued_income: r.alpha_accrued_income,
            accrued_income_diff: r.accrued_income_diff,
            match_status: r.match_status,
            unmatched_count: r.unmatched_count,
        }
    }
}

#[derive(Serialize, Deserialize, PartialEq, Debug, Clone, Copy, ToSchema)]
pub enum MatchStatusQuery {
    Matched,
    UnMatched,
    PartiallyMatched,
    NotPresentInSource,
    NotPresentInAlpha,
}

impl From<MatchStatusQuery> for MatchStatus {
    fn from(status: MatchStatusQuery) -> Self {
        match status {
            MatchStatusQuery::Matched => MatchStatus::Matched,
            MatchStatusQuery::UnMatched => MatchStatus::UnMatched,
            MatchStatusQuery::PartiallyMatched => MatchStatus::PartiallyMatched,
            MatchStatusQuery::NotPresentInSource => MatchStatus::NotPresentInSource,
            MatchStatusQuery::NotPresentInAlpha => MatchStatus::NotPresentInAlpha,
        }
    }
}
