use std::collections::HashMap;

use chrono::NaiveDate;
use rust_decimal::Decimal;
use rust_decimal_macros::dec;

use crate::states::investment::Investment;

use super::{
    enums::{TransactionSubType, TransactionType},
    ledger::{Ledger, LedgerTransaction},
    receivable_payable::ReceivablePayable,
};

#[derive(rkyv::Deserialize, rkyv::Serialize, rkyv::Archive, Default, Debug)]
pub struct Portfolio {
    pub date: NaiveDate,
    pub portfolio_id: String,
    pub client_id: String,
    pub name: String,
    pub balance: Decimal,
    pub assets_and_liabilities: Decimal,
    pub payables: Decimal,
    pub receivables: Decimal,
    pub absolute_return_value: Decimal,
    pub absolute_return_percent: Decimal,
    pub total_capital: Decimal,
    pub invested_capital: Decimal,
    pub withdrawals: Decimal,
    pub market_value: Decimal,
    pub dividends_paid: Decimal,
    pub dividend_reinvested: Decimal,
    pub market_value_change: Decimal,
    pub market_value_change_percent: Decimal,
    pub realised_gain_loss: Decimal,
    pub unrealised_gain_loss: Decimal,
    pub xirr: Decimal,
    pub xirr_unrealised: Decimal,
    pub twrr: Decimal,
}

impl Portfolio {
    /// Applied the investments in the Portfolio and
    /// Updates the MarketValue, MarketValueChange, RealisedGainsLoss,AbsoluteRet,AbsoluteRetPercent
    pub fn apply_investments(&mut self, investments: &Vec<Investment>) {
        //Compute the Market Value and update it
        let previous_market_value = self.market_value;
        let market_value: Decimal = investments
            .iter()
            .map(|inv| {
                if inv.market_value > dec!(0) {
                    inv.market_value
                } else {
                    dec!(0)
                }
            })
            .sum();
        self.market_value = market_value;

        self.market_value_change = market_value - previous_market_value;

        //Compute the realised gains
        let realised_gains_of_portfolio = investments.iter().map(|inv| inv.realised_gain_loss).sum();
        self.realised_gain_loss = realised_gains_of_portfolio;

        let absolute_return_value =
            self.market_value + self.dividends_paid + investments.iter().map(|inv| inv.dividends_paid).sum::<Decimal>()
                - self.total_capital;

        self.absolute_return_percent = absolute_return_value;

        let absolute_return_percentage = (self.market_value + self.dividends_paid / self.total_capital) * dec!(100);

        self.absolute_return_percent = absolute_return_percentage;
    }

    /// Updates the Cash balance of the portfolio
    pub fn update_balance(&mut self, balance: Decimal) {
        self.balance += balance;
    }

    /// Updates the Unrealised Gain Loss for the Portfolio
    pub fn update_unrealised_gain_loss(&mut self) {
        self.unrealised_gain_loss = self.market_value + self.balance - self.invested_capital;
    }

    /// Process the all transactions from InvestmentTransactions,CapitalRegister,PeakMargin, IncomeExpense
    /// And Generate Ledger entries for the Portfolio
    pub fn process_ledger(&mut self, mut transactions: Vec<&dyn LedgerTransaction>) -> Vec<Ledger> {
        let mut ledgers = Vec::new();
        let mut current_balance = self.balance;
        let mut current_assets_and_liabilities = self.assets_and_liabilities;

        let mut previous_running_balance = current_balance;
        let mut previous_assets_and_liabilities = current_assets_and_liabilities;

        transactions.sort_by_key(|f| f.get_transaction_date());

        for txn in transactions {
            let txn_type = txn.get_transaction_type();
            let txn_sub_type = txn.get_transaction_sub_type();
            let txn_amount = txn.get_amount();
            let txn_date = txn.get_transaction_date();
            let settlement_date = txn.get_settlement_date();

            //FOR Legacy Ledger
            if txn_type == TransactionType::Credit {
                current_balance += txn_amount;
            } else if txn_type == TransactionType::Debit {
                current_balance -= txn_amount;
            }

            if txn_sub_type == TransactionSubType::OtherAssetsLiabilities {
                if txn_type == TransactionType::Credit {
                    current_assets_and_liabilities -= txn_amount;
                } else if txn_type == TransactionType::Debit {
                    current_assets_and_liabilities += txn_amount;
                }
            }

            //That means this transaction didn't change the balance
            //SO don't add to ledger
            // This can occur when we fetch transaction that is not required for ledger entry
            if current_balance != previous_running_balance {
                let ledger = Ledger {
                    running_balance: current_balance,
                    amount: txn_amount,
                    description: txn.get_description(),
                    settlement_date: txn.get_settlement_date(),
                    transaction_date: txn.get_transaction_date(),
                    transaction_sub_type: txn.get_transaction_sub_type(),
                    transaction_type: txn.get_transaction_type(),
                };

                ledgers.push(ledger);
                previous_running_balance = current_balance;
            }

            if current_assets_and_liabilities != previous_assets_and_liabilities {
                let ledger = Ledger {
                    running_balance: current_assets_and_liabilities,
                    amount: txn_amount,
                    description: txn.get_description(),
                    settlement_date: txn.get_settlement_date(),
                    transaction_date: txn.get_transaction_date(),
                    transaction_sub_type: txn.get_transaction_sub_type(),
                    transaction_type: txn.get_transaction_type(),
                };

                ledgers.push(ledger);
                previous_assets_and_liabilities = current_assets_and_liabilities;
            }
        }

        //Update the Cash Of Porfolio
        self.balance = current_balance;
        self.assets_and_liabilities = current_assets_and_liabilities;

        ledgers
    }

    /// Update cumulative TWRR with new daily TWRR
    pub fn update_cumulative_twrr(&mut self, daily_twrr: Decimal) {
        self.twrr = (dec!(1.0) + self.twrr) * (dec!(1.0) + daily_twrr) - dec!(1.0);
    }

    /// This returns the Actual Cash of the Portfolio After Applying the Receivables and Payables
    /// It doesn't change the cash of the portfolio until settlement has happened
    /// But account into the calculation of AUM
    pub fn process_receivables_payables(
        &mut self,
        receivables_payables: &Vec<ReceivablePayable>,
    ) -> (HashMap<NaiveDate, Decimal>, HashMap<NaiveDate, Decimal>) {
        let mut current_receivables = self.receivables;

        let mut current_payables = self.payables;

        let mut settlement_payable_map = HashMap::new();
        let mut settlement_receivable_map = HashMap::new();

        for rec in receivables_payables {
            if rec.transaction_type == TransactionType::Credit {
                current_receivables += rec.amount;

                let value = settlement_receivable_map
                    .get(&rec.settlement_date.date())
                    .unwrap_or(&dec!(0))
                    .clone();
                settlement_receivable_map.insert(rec.settlement_date.date(), value + rec.amount);
                
            } else {
                current_payables += rec.amount;
                let value = settlement_payable_map
                    .get(&rec.settlement_date.date())
                    .unwrap_or(&dec!(0))
                    .clone();
                settlement_payable_map.insert(rec.settlement_date.date(), value + rec.amount);
            }
        }

        self.receivables = current_receivables;
        self.payables = current_payables;

        (settlement_receivable_map, settlement_payable_map)
    }
}
