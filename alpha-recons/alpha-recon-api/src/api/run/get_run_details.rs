use crate::error::ApiError;
use crate::models::{AppState, recon_project::ReconProjectRsp, recon_project_runs::ReconProjectRunDetailsRsp};
use alpha_core_db::tenant_clickhouse::reconciliation::{project::ReconProject, recon_run::ReconProjectRuns};
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use serde::Serialize;
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct GetReconRunDetailsRsp {
    project: ReconProjectRsp,
    run: ReconProjectRunDetailsRsp,
}

#[utoipa::path(
    get,
    tag = "Recon runs",
    path = "/recon-run/get-run-details/{run_id}",
    params(
        ("run_id" = String, Path, description = "Run ID")
    ),
    responses(
        (status = 200, description = "Run details found", body = GetReconRunDetailsRsp),
        (status = 404, description = "Run details not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_run_details(
    State(state): State<Arc<AppState>>,
    Path(run_id): Path<String>,
) -> Result<Json<GetReconRunDetailsRsp>, impl IntoResponse> {
    let run = match ReconProjectRuns::get_by_id(&state.tenant_clickhouse, &run_id).await {
        Ok(run) => run,
        Err(err) => {
            return Err(ApiError::NotFound {
                message: "Requested run not found".to_string(),
                status: false,
            });
        }
    };

    let project_details = match ReconProject::get_by_id(&state.tenant_clickhouse, &run.project_id).await {
        Ok(project) => project,
        Err(err) => {
            return Err(ApiError::NotFound {
                message: "Requested project not found".to_string(),
                status: false,
            });
        }
    };

    Ok(Json(GetReconRunDetailsRsp {
        project: project_details.into(),
        run: run.into(),
    }))
}
