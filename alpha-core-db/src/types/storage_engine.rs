use chrono::NaiveDate;
use deadpool::managed::Object;

use crate::{connection::pool::Manager, error::DatabaseError, schema::cash_ledger::PortfolioCashLedger};

#[derive(Debug)]
pub struct StorageEngineTransactions {
    pub capital_registers: String,
    pub investment_transactions: String,
    pub peak_margin_logs: String,
    pub cash_ledgers: String,
    pub receivable_payables: String,
}

impl StorageEngineTransactions {
    /// Returns All the eligible Transaction from DB in string
    /// It should be parsed to json the function whichever calls this
    pub async fn get_txns_by_dates(
        conn: &mut Object<Manager>,
        id: &str,
        from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> Result<String, DatabaseError> {
        let query = format!(
            "
                    WITH DateRange AS (
                    SELECT TOP (DATEDIFF(DAY, '{}', '{}') + 1)
                    DATEADD(DAY, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) - 1, '{}') AS TransactionDate
                    FROM sys.objects s1
                    CROSS JOIN sys.objects s2
                    )
                    
                    SELECT
                    CAST(d.TransactionDate AS DATE) AS [date],

                    ISNULL((
                    SELECT  
                            pcl.SettlementDate,
                            pcl.TransactionType,
                            pcl.TransactionSubType,
                            pcl.Amount,
                            pcl.ModelportfolioId,
                            pcl.CreatedDate,
                            pcl.LastUpdatedDate,
                            pcl.Description,
                            pcl.TransactionDate
                    FROM PortfolioCashLedgers pcl
                    WHERE CAST(pcl.TransactionDate AS DATE)  = d.TransactionDate
                    AND pcl.PortfolioId = '{}'
                    FOR JSON PATH
                    ), '[]') AS cashLedger,

                    ISNULL((
                    SELECT pcr.*
                    FROM PortfolioCapitalRegisters pcr
                    WHERE CAST(pcr.TransactionDate AS DATE) = CAST(d.TransactionDate AS DATE)
                    AND pcr.PortfolioId = '{}'
                    FOR JSON PATH
                    ), '[]') AS capitalRegisters,

                    ISNULL((
                    SELECT it.*
                    FROM InvestmentTransactions it
                    WHERE CAST(it.TransactionDate AS DATE) = CAST(d.TransactionDate AS DATE)
                    AND it.PortfolioId = '{}'
                    FOR JSON PATH
                    ), '[]') AS investmentTransactions,

                    ISNULL((
                    SELECT it.*
                    FROM PeakMarginLogs it
                    WHERE CAST(it.Date AS DATE) = CAST(d.TransactionDate AS DATE)
                    AND it.PortfolioId = '{}'
                    FOR JSON PATH
                    ), '[]') AS peakMarginTransactions,

                    ISNULL((
                    SELECT it.*
                    FROM PortfolioReceivables it
                    WHERE CAST(it.TransactionDate AS DATE) = CAST(d.TransactionDate AS DATE) AND CAST(it.TransactionDate AS DATE) < CAST(it.SettlementDate AS DATE)
                    AND it.PortfolioId = '{}'
                    FOR JSON PATH
                    ), '[]') AS receivablePayables

                    FROM DateRange d
                    ORDER BY d.TransactionDate
                    FOR JSON PATH
        ",
            from_date, to_date, from_date, id, id, id, id, id
        );

        let rows = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })
            .unwrap()
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })
            .unwrap();
        let mut json_string_buff = String::new();
        for row in rows {
            let r = row.get::<&str, _>(0).unwrap().to_string();
            json_string_buff.push_str(&r);
        }

        Ok(json_string_buff)
    }

    pub async fn get_txns_for_genesis(conn: &mut Object<Manager>, id: &str, date: NaiveDate) -> Self {
        let query = format!(
            "
        SELECT 
            ISNULL((
                SELECT *
                FROM PortfolioCapitalRegisters PCR
                WHERE PCR.PortfolioId = '{}'
                AND CAST(PCR.TransactionDate AS DATE) = CAST('{}' AS DATE)
                FOR JSON PATH
            ), '[]') as CapitalRegisters,

            ISNULL((
                SELECT *
                FROM InvestmentTransactions IT
                WHERE IT.PortfolioId = '{}'
                AND CAST(IT.TransactionDate AS DATE) = CAST('{}' AS DATE)
                FOR JSON PATH
            ), '[]') as InvestmentTransactions,

            ISNULL((
                SELECT *
                FROM PeakMarginLogs PML
                WHERE PML.PortfolioId = '{}'
                AND CAST(PML.Date AS DATE) = CAST('{}' AS DATE)
                FOR JSON PATH
            ), '[]') as PeakMarginLogs,

            ISNULL((
                SELECT *
                FROM PortfolioCashLedgers PCL
                WHERE PCL.PortfolioId = '{}'
                AND CAST(PCL.TransactionDate AS DATE) = CAST('{}' AS DATE)
                AND PCL.TransactionType IN ('Credit','Debit')
                FOR JSON PATH
            ), '[]') as CashLedgers,

            ISNULL((
                SELECT it.*
                FROM PortfolioReceivables it
                WHERE CAST(it.TransactionDate AS DATE) = CAST('{}' AS DATE) AND CAST(it.TransactionDate AS DATE) < CAST(it.SettlementDate AS DATE)
                AND it.PortfolioId = '{}'
                FOR JSON PATH
                ), '[]') AS ReceivablePayabales

        ",
            id, date, id, date, id, date, id, date, date, id
        );

        let row = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })
            .unwrap()
            .into_row()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })
            .unwrap()
            .unwrap();

        let capital_registers = row.get::<&str, _>("CapitalRegisters").unwrap().to_string();
        let investment_transactions = row.get::<&str, _>("InvestmentTransactions").unwrap().to_string();
        let peak_margin_logs = row.get::<&str, _>("PeakMarginLogs").unwrap().to_string();
        let cash_ledgers = row.get::<&str, _>("CashLedgers").unwrap().to_string();
        let receivable_payables = row.get::<&str, _>("ReceivablePayabales").unwrap().to_string();

        Self {
            capital_registers,
            cash_ledgers,
            investment_transactions,
            peak_margin_logs,
            receivable_payables,
        }
    }
}

#[cfg(test)]
mod tests {
    use chrono::NaiveDate;

    use crate::{connection::connect_to_mssql, types::storage_engine::StorageEngineTransactions};

    #[tokio::test]
    async fn test_storage() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(10).await;
        let mut conn = pool.get().await.unwrap();
        let id = String::from("f32216dd041f49f696a45988939d3da4");
        let from_date = NaiveDate::from_ymd_opt(2024, 07, 22).unwrap();
        let to_date = NaiveDate::from_ymd_opt(2024, 07, 30).unwrap();
        let foo = StorageEngineTransactions::get_txns_by_dates(&mut conn, &id, from_date, to_date).await;

        println!("{:?}", foo);
    }
}
