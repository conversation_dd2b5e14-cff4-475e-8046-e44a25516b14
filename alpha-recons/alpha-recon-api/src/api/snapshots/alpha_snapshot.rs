use crate::{
    error::ApiError,
    models::{AppState, alpha_snapshot_resp::AlphaSnapshotResp},
};
use alpha_core_db::tenant_clickhouse::reconciliation::alpha_snapshot::{AlphaSnapshot, AlphaSnapshotFilter};
use axum::{
    <PERSON><PERSON>,
    extract::{Path, Query, State},
    http::{HeaderMap, HeaderValue, StatusCode, header},
    response::IntoResponse,
};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Debug, ToSchema, Clone, serde::Deserialize)]
pub struct GetAlphaSnashotReqParam {
    #[schema(value_type = Option<String>, example = "Client")]
    client_code: Option<String>,
    #[schema(value_type = Option<String>, example = "Security")]
    security_name: Option<String>,
    #[schema(value_type = Option<String>, example = "ISIN")]
    isin: Option<String>,
    #[schema(value_type = Option<String>, example = "Symbol")]
    symbol: Option<String>,
    #[schema(value_type = u64, example = "10")]
    limit: u64,
    #[schema(value_type = u64, example = "0")]
    offset: u64,
}

#[derive(Debug, ToSchema, Clone, serde::Serialize)]
#[serde(rename_all = "camelCase")]
pub struct GetAlphaSnapshotResp {
    #[schema(value_type = u64, example = "100")]
    total_records: u64,
    #[schema(value_type = u64, example = "10")]
    current_offset: u64,
    results: Vec<AlphaSnapshotResp>,
}

#[utoipa::path(
    get,
    tag = "Snapshot",
    path = "/snapshots/get-alpha-snapshot/{run_id}",
    params(
        ("run_id" = String, Path, description = "Run ID"),
        ("offset" = String, Query, description = "Offset of the pagination"),
        ("limit" = String, Query, description = "Total number of records in a single request"),
        ("securityName" = Option<String>, Query, description = "Security name to filter"),
        ("isin" = Option<String>, Query, description = "ISIN to filter"),
        ("symbol" = Option<String>, Query, description = "Symbol to filter"),
        ("clientCode" = Option<String>, Query, description = "ClientCode to filter")
    ),
    responses(
        (status = 200, description = "Alpha snapshot fetched successfully"),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_alpha_snapshot(
    State(state): State<Arc<AppState>>,
    Path(run_id): Path<String>,
    Query(req_param): Query<GetAlphaSnashotReqParam>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let filter = AlphaSnapshotFilter {
        security_name: req_param.security_name,
        isin: req_param.isin,
        symbol: req_param.symbol,
        client_code: req_param.client_code,
    };

    let alpha_snapshot_result = AlphaSnapshot::get_filtered(
        &state.tenant_clickhouse,
        &run_id,
        filter,
        req_param.limit,
        req_param.offset,
    )
    .await;

    match alpha_snapshot_result {
        Ok((total_records, snapshot_res)) => {
            let results = snapshot_res.into_iter().map(Into::into).collect();

            return Ok((
                StatusCode::OK,
                Json(GetAlphaSnapshotResp {
                    results,
                    total_records,
                    current_offset: req_param.offset,
                }),
            )
                .into_response());
        }
        Err(e) => {
            println!("Error fetching alpha snapshot: {}", e);
            Err(ApiError::InternalError {
            message: format!("Failed to retrieve alpha snapshot, please try again"),
            status: false,
        })},
    }
}
