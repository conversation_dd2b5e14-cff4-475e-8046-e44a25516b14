trigger:
  branches:
    include:
    - staging
  paths:
    exclude:
    - manifests

resources:
- repo: self

variables:
  awsRegion: 'ap-south-1'
  imageRepository: 'staging/alpha-core' 
  containerRegistry: '540008527082.dkr.ecr.ap-south-1.amazonaws.com'
  dockerfilePath: 'Dockerfile'
  vmImageName: 'ubuntu-latest'
  DOCKER_BUILDKIT: 1
  COMPOSE_DOCKER_CLI_BUILD: 1
  baseVersion: "v0.1.0"
stages:
- stage: Build
  displayName: Build and push stage
  jobs:
  - job: Build
    timeoutInMinutes: 120 # for first time build on every branch
    displayName: Build
    pool:
      vmImage: $(vmImageName)
    steps:
    - checkout: self
      persistCredentials: 'true'
      displayName: 'Checkout repository'
    
    # Caching the registry directory for cross-run
    - task: Cache@2
      inputs:
        key: 'cargo-registry | "$(Agent.OS)" | Cargo.lock'
        restoreKeys: 'cargo-registry | "$(Agent.OS)"'
        path: |
          $(HOME)/.cargo/registry
      displayName: 'Cache Cargo'
    # Caching the target directory for cross-run
    - task: Cache@2
      inputs:
        key: '"cargo-target" | "$(Agent.OS)" | Cargo.lock'
        restoreKeys: |
          "cargo-target" | "$(Agent.OS)"
        path: $(Build.SourcesDirectory)/target
      displayName: 'Cache Cargo Target'

    # Download SSH key from the azure secure file (require to access private git repos)
    - task: DownloadSecureFile@1
      name: downloadSSHKey
      inputs:
        secureFile: ssh_access_keys

    # Build alpha-stp-crons binary 
    - script: |
        mkdir -p ~/.ssh
        ssh-keyscan ssh.dev.azure.com >> ~/.ssh/known_hosts
        chmod 600 $(downloadSSHKey.secureFilePath)
        eval $(ssh-agent -s)
        ssh-add $(downloadSSHKey.secureFilePath)
        echo "Loaded SSH keys:"
        ssh-add -l

        cargo build --release --locked --package alpha-stp-crons
      displayName: 'Build alpha-stp-crons binary'
      env:
        SSH_AUTH_SOCK: $(SSH_AUTH_SOCK)

    # Upload the compiled alpha-stp-crons binary to MinIO Storage    
    - script: |
        # chmod +x upload.sh
        ./upload.sh
      displayName: 'Upload binary to MinIO'

    #Login to aws
    - script: |
        aws configure set aws_access_key_id $(AWS_ACCESS_KEY_ID)
        aws configure set aws_secret_access_key $(AWS_SECRET_ACCESS_KEY)
        aws configure set default.region $(awsRegion)
      env:
        AWS_ACCESS_KEY_ID: $(AWS_ACCESS_KEY_ID)
        AWS_SECRET_ACCESS_KEY: $(AWS_SECRET_ACCESS_KEY)
      displayName: 'Configure AWS Credentials'

    # Login to ECR & build the latest docker image
    - script: |
        aws ecr get-login-password --region $(awsRegion) | docker login --username AWS --password-stdin $(containerRegistry)
      displayName: 'Login to AWS ECR'
    
    # Build docker image with previously stored buildcache
    - script: |
        mkdir -p ~/.ssh
        ssh-keyscan ssh.dev.azure.com >> ~/.ssh/known_hosts
        chmod 600 $(downloadSSHKey.secureFilePath)
        eval $(ssh-agent -s)
        ssh-add $(downloadSSHKey.secureFilePath)
        echo "Loaded SSH keys:"
        ssh-add -l
        
        # Setup buildx builder (docker-container driver)
        docker buildx create --use --name buildx_builder
        docker buildx inspect buildx_builder --bootstrap

        echo "-----------------Building docker img with cache-from and cache-to----------------"
        docker pull $(containerRegistry)/$(imageRepository):buildcache \
        || echo "No previous image found — full build will run"

        docker buildx build \
          --ssh default=$SSH_AUTH_SOCK \
          --cache-from=type=registry,ref=$(containerRegistry)/$(imageRepository):buildcache \
          --cache-to=type=registry,ref=$(containerRegistry)/$(imageRepository):buildcache,mode=max \
          --push \
          --progress=plain \
          -t $(containerRegistry)/$(imageRepository):latest \
          -f $(dockerfilePath) .
      displayName: 'Build and push with BuildX'
      env:
        SSH_AUTH_SOCK: $(SSH_AUTH_SOCK)

    - script: |
        tag="${baseVersion}+build-$(Build.BuildId)"
        echo "##vso[task.setvariable variable=finalTag]$tag"
      displayName: "Generate SemVer Tag"
      name: SetTag

    - script: |
        git config --global user.email "<EMAIL>"
        git config --global user.name "Azure Pipeline"
        git fetch origin
        git checkout -B staging origin/staging
        echo "Current branch: $(git branch --show-current)"
        echo "Setting tag to $finalTag"
        yq eval ".spec.values.tag = \"$finalTag\"" -i manifests/staging/release/release.yaml
        git add manifests/staging/release/release.yaml
        git commit -m "chore: bump tag to $finalTag"
        git push origin staging
      displayName: "Update and Push HelmRelease"
      env:
        finalTag: $(finalTag)