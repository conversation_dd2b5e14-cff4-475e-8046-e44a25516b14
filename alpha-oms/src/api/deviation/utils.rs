use actlogica_logs::{builder::Log<PERSON>uilder, log_error, log_info};
use alpha_core_db::schema::oms::PortfolioDetailsForOms;
use alpha_utils::constants::RedisKey;
use anyhow::{anyhow, Context};
use axum::{http::StatusCode, Json};
use deadpool_redis::Connection;
use serde_json::json;



pub async fn get_deviation_portfolios(
    redis_conn: &mut Connection,
    portfolio_ids: &Vec<String>,
) -> anyhow::Result<Vec<PortfolioDetailsForOms>> {
    log_info(LogBuilder::system("Getting Portfolio Details from Redis"));

    let mut pipeline = deadpool_redis::redis::pipe();

    let mut portfolios: Vec<PortfolioDetailsForOms> = Vec::with_capacity(portfolio_ids.len());

    for portfolio_id in portfolio_ids {
        let key = format!("{}:{}", RedisKey::DeviationPortfolios, portfolio_id);
        pipeline.get(key);
    }

    let portfolio_results: Vec<Option<Vec<u8>>> = pipeline
        .query_async::<Vec<Option<Vec<u8>>>>(redis_conn)
        .await
        .map_err(|_| {
            log_error(LogBuilder::system("Failed to get portfolio details from Redis"));
            anyhow!("Failed to get portfolio details")
        })
        .context("Failed to get portfolio details")?;

    for portfolio in portfolio_results {
        if let Some(portfolio) = portfolio {
            let portfolio_for_oms: Result<PortfolioDetailsForOms, serde_json::Error> =
                serde_json::from_slice(&portfolio);

            if let Ok(portfolio) = portfolio_for_oms {
                portfolios.push(portfolio);
            } else {
                log_error(LogBuilder::system("Failed to deserialise"));
                return Err(anyhow!("Failed to deserialise"));
            }
        } else {
            log_error(LogBuilder::system("Portfolio Details is missing"));
            return Err(anyhow!("Portfolio Details is missing"));
        }
    }

    Ok(portfolios)
}

pub async fn fetch_reports_in_bulk(
    redis_conn: &mut Connection,
    key: &str,
    report_ids: &[u64],
) -> Result<Vec<Option<Vec<String>>>, (StatusCode, Json<serde_json::Value>)> {
    log_info(LogBuilder::system("Fetching reports in bulk from Redis"));
    
    let mut pipeline = deadpool_redis::redis::pipe();

    for report_id in report_ids {
        pipeline.zrangebyscore(key, *report_id, *report_id);
    }

    let results = pipeline
        .query_async::<Vec<Option<Vec<String>>>>(redis_conn)
        .await
        .map_err(|_| {
            let error_response = json!({
                "status": "error",
                "message": "Failed to fetch reports in bulk",
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

    Ok(results)
}

