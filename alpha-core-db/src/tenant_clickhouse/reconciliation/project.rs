use chrono::{DateTime, NaiveDate, NaiveDateTime, Utc};
use clickhouse::{Client, Row};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct ReconProject {
    #[serde(rename = "Id")]
    pub id: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Description")]
    pub description: String,

    #[serde(rename = "UserId")]
    pub user_id: String,

    #[serde(rename = "UserName")]
    pub user_name: String,

    #[serde(rename = "NumOfRuns")]
    pub num_of_runs: i32,

    #[serde(rename = "GoldenSourceFile")]
    pub golden_source_file: String,

    #[serde(rename = "OffSetDate", with = "clickhouse::serde::chrono::date")]
    pub off_set_date: NaiveDate,

    #[serde(rename = "CreatedAt", with = "clickhouse::serde::chrono::datetime64::millis")]
    pub created_at: DateTime<Utc>,

    #[serde(rename = "UpdatedAt", with = "clickhouse::serde::chrono::datetime64::millis")]
    pub updated_at: DateTime<Utc>,

    #[serde(rename = "EndedAt", with = "clickhouse::serde::chrono::datetime64::millis::option")]
    pub ended_at: Option<DateTime<Utc>>,

    #[serde(rename = "IsActive")]
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct AllProjectResponse {
    #[serde(rename = "Id")]
    pub id: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Description")]
    pub description: String,

    #[serde(rename = "OffSetDate", with = "clickhouse::serde::chrono::date")]
    pub off_set_date: NaiveDate,

    #[serde(rename = "CreatedAt", with = "clickhouse::serde::chrono::datetime64::millis")]
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct ProjectResponse {
    #[serde(rename = "Id")]
    pub id: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Description")]
    pub description: String,

    #[serde(rename = "UserName")]
    pub user_name: String,

    #[serde(rename = "NumOfRuns")]
    pub num_of_runs: i32,

    #[serde(rename = "GoldenSourceFile")]
    pub golden_source_file: String,

    #[serde(rename = "OffSetDate", with = "clickhouse::serde::chrono::date")]
    pub off_set_date: NaiveDate,

    #[serde(rename = "CreatedAt", with = "clickhouse::serde::chrono::datetime64::millis")]
    pub created_at: DateTime<Utc>,

    #[serde(rename = "UpdatedAt", with = "clickhouse::serde::chrono::datetime64::millis")]
    pub updated_at: DateTime<Utc>,

    #[serde(rename = "EndedAt", with = "clickhouse::serde::chrono::datetime64::millis::option")]
    pub ended_at: Option<DateTime<Utc>>,
    #[serde(rename = "IsActive")]
    pub is_active: bool,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct ReconScopeDates {
    #[serde(rename = "OffSetDate", with = "clickhouse::serde::chrono::date")]
    pub off_set_date: NaiveDate,
}

impl ReconProject {
    pub async fn insert(conn: &Client, project: ReconProject) -> Result<(), clickhouse::error::Error> {
        let mut client = conn.insert::<Self>("ReconProject")?;
        client.write(&project).await.unwrap();
        client.end().await.unwrap();
        Ok(())
    }

    pub async fn get_by_id(conn: &Client, id: &str) -> Result<ProjectResponse, clickhouse::error::Error> {
        let query = format!(
            "SELECT
                Id,
                Name,
                Description,
                UserName,
                NumOfRuns,
                GoldenSourceFile,
                OffSetDate,
                CreatedAt,
                UpdatedAt,
                EndedAt,
                IsActive
            FROM ReconProject
            WHERE Id = '{}'",
            id
        );

        let result = conn.query(&query).fetch_one::<ProjectResponse>().await;
        match result {
            Ok(project) => Ok(project),
            Err(e) => Err(e),
        }
    }

    pub async fn get_golden_source_url(conn: &Client, id: String) -> Result<String, clickhouse::error::Error> {
        let query = format!("SELECT GoldenSourceFile FROM ReconProject WHERE Id = '{}'", id);
        let result = conn.query(&query).fetch_one::<String>().await;
        match result {
            Ok(url) => Ok(url),
            Err(e) => Err(e),
        }
    }

    pub async fn get_all(conn: &Client, user_id: &str) -> Result<Vec<ProjectResponse>, clickhouse::error::Error> {
        let query = format!(
            "SELECT
                Id,
                Name,
                Description,
                UserName,
                NumOfRuns,
                GoldenSourceFile,
                OffSetDate,
                CreatedAt,
                UpdatedAt,
                EndedAt,
                IsActive
            FROM ReconProject
            WHERE UserId = '{}'",
            user_id
        );
        let result = conn.query(&query).fetch_all::<ProjectResponse>().await;
        match result {
            Ok(projects) => Ok(projects),
            Err(e) => Err(e),
        }
    }

    pub async fn get_project_scope_dates(
        conn: &Client,
        project_id: &str,
    ) -> Result<ReconScopeDates, clickhouse::error::Error> {
        let query = format!("SELECT OffSetDate FROM ReconProject WHERE Id = '{}'", project_id);
        let project_scope_dates = conn.query(&query).fetch_one::<ReconScopeDates>().await;
        match project_scope_dates {
            Ok(project_scope_dates) => Ok(project_scope_dates),
            Err(e) => Err(e),
        }
    }

    pub async fn update_date(conn: &Client, project_id: &str) -> Result<(), clickhouse::error::Error> {
        let date = Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
        let query = format!(
            "ALTER TABLE ReconProject UPDATE UpdatedAt = '{}' WHERE Id = '{}'",
            date, project_id
        );
        let result = conn.query(&query).execute().await;
        match result {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }

    pub async fn add_run(conn: &Client, project_id: &str) -> Result<(), clickhouse::error::Error> {
        let date = Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
        let query = format!(
            "ALTER TABLE ReconProject UPDATE NumOfRuns = NumOfRuns + 1, UpdatedAt = '{}' WHERE Id = '{}'",
            date, project_id
        );
        let result = conn.query(&query).execute().await;
        match result {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }
    pub async fn end_project(conn: &Client, project_id: &str) -> Result<(), clickhouse::error::Error> {
        let date = Utc::now().format("%Y-%m-%d %H:%M:%S%.3f").to_string();
        let query = format!(
            "UPDATE ReconProject SET IsActive = false, UpdatedAt = '{}', EndedAt = '{}' WHERE Id = '{}'",
            date, date, project_id
        );
        let result = conn.query(&query).execute().await;
        match result {
            Ok(_) => Ok(()),
            Err(e) => Err(e),
        }
    }

    pub async fn is_active(conn: &Client, project_id: &str) -> Result<bool, clickhouse::error::Error> {
        let query = format!("SELECT IsActive FROM ReconProject WHERE Id = '{}'", project_id);
        let result = conn.query(&query).fetch_one::<bool>().await;
        match result {
            Ok(is_active) => Ok(is_active),
            Err(e) => Err(e),
        }
    }
}
