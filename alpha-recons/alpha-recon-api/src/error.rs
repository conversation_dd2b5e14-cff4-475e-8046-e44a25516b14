use axum::{
    Json,
    http::StatusCode,
    response::{IntoResponse, Response},
};
use serde::Serialize;
use serde_json::json;
use thiserror::Error;
use utoipa::ToSchema;

#[derive(Error, Debug)]
pub enum ApiError {
    #[error("{message}")]
    NotFound { message: String, status: bool },
    #[error("{message}")]
    InternalError { message: String, status: bool },
    #[error("{message}")]
    CreationFailed { message: String },
    #[error("{message}")]
    BadRequest { message: String },
}

/// TODO: Convert all response to this schema instead of using json macro
#[derive(Serialize, ToSchema)]
pub struct ErrorResponse {
    message: String,
    status: bool,
}

//Impl IntoResponse for the Error
impl IntoResponse for ApiError {
    fn into_response(self) -> Response {
        match self {
            ApiError::NotFound { message, status } => {
                let payload = json!({
                        "message":message,
                        "status":status
                });
                (StatusCode::BAD_REQUEST, Json(payload)).into_response()
            }
            ApiError::InternalError { message, status } => {
                let payload = json!({
                        "message":message,
                        "status":status
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(payload)).into_response()
            }
            ApiError::CreationFailed { message } => {
                let payload = json!({
                        "message":message,
                        "status": false
                });
                (StatusCode::BAD_REQUEST, Json(payload)).into_response()
            }
            ApiError::BadRequest { message } => {
                let payload = json!({
                        "message":message,
                        "status": false
                });
                (StatusCode::BAD_REQUEST, Json(payload)).into_response()
            }
        }
    }
}
