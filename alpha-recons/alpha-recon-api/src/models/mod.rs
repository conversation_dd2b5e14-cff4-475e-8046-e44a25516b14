pub mod alpha_snapshot_resp;
pub mod recon_project;
pub mod recon_project_runs;
pub mod recon_result;
pub mod report;
pub mod response;
pub mod run;
use crate::config::Config;
use alpha_core_db::{
    connection::pool::{Manager, deadpool::managed::Pool},
    tenant_clickhouse::{connect_tenant_clickhouse_client, connect_tenant_recon_clickhouse_client},
};
use alpha_utils::rabbit_mq::RabbitMq;
use deadpool_redis::{PoolConfig, Timeouts};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::time::Duration;

#[allow(non_snake_case)]
#[derive(Debug, Deserialize, Serialize, Clone)]
pub struct User {}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct TokenClaims {
    pub exp: u64,
    pub iss: String,
    pub aud: Vec<String>,
    pub nonce: Option<String>,
    pub iat: Option<u64>,
    pub at_hash: Option<String>,
    pub sid: Option<String>,
    pub sub: String,
    pub auth_time: u64,
    pub idp: String,
    pub organisation_logo: String,
    pub organisation_id: Option<String>,
    pub name: String,
    pub email: String,
    pub subscription: String,
    pub feature: Option<Vec<String>>,
    pub tenant: String,
    pub admin_id: Option<String>,
    pub role: Value,
    pub amr: Vec<String>,
    pub scope: Vec<String>,
}

#[derive(Clone)]
pub struct AppState {
    pub env: Config,
    pub db: Pool<Manager>,
    pub master_db: Pool<Manager>,
    pub tenant_redis_url: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pub master_redis_url: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    pub tenant_clickhouse: clickhouse::Client,
    pub tenant_minio_client: minio::s3::Client,
}

impl AppState {
    pub async fn new() -> Self {
        let config = Config::init();
        let db = alpha_core_db::connection::connect_to_mssql(config.mssql_pool_size).await;
        let master_db = alpha_core_db::connection::connect_to_master_data(config.master_data_pool_size).await;
        let mut cfg = deadpool_redis::Config::from_url(config.tenant_redis_url.clone());
        let tenant_minio_client = alpha_core_db::minio::connect_to_minio()
            .await
            .expect("FAILED to connect to MinIO");

        cfg.pool = Some(PoolConfig {
            timeouts: Timeouts {
                create: Some(Duration::from_secs(10)),
                recycle: Some(Duration::from_secs(10)),
                wait: Some(Duration::from_secs(10)),
            },
            ..Default::default()
        });

        let tenant_redis_pool = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let tenant_clickhouse = connect_tenant_recon_clickhouse_client();

        let master_redis_pool = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        AppState {
            env: config,
            db,
            master_db,
            tenant_redis_url: tenant_redis_pool,
            master_redis_url: master_redis_pool,
            tenant_clickhouse,
            tenant_minio_client,
        }
    }
}
