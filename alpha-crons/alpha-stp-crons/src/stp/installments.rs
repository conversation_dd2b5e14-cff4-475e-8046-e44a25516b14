use super::{find_next_working_day, is_holiday};
use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    schema::{
        client_order_entry::OrderStatus,
        portfolio::{portfolio_service::PortfolioServiceSTP, Portfolio},
        stp::{deployment_tracker::DeploymentTracker, systemetic_deployment::SystematicDeployment},
    },
    transactions::{commit_transaction, start_transaction},
};
use alpha_oms::oms::{save_orders, OrderComputer};
use chrono::Utc;

pub async fn trigger_installments_for_today() -> Result<(), String> {
    // log before starting the process
    log_info(LogBuilder::system(&format!(
        "Triggered Installments for: {}",
        Utc::today().naive_local()
    )));

    // get the db connection
    let app_state = alpha_oms::models::AppState::new().await;
    let pool = app_state.db.clone();
    let mut conn = pool.get().await.unwrap();

    // get list of all active installments
    let mut active_installments = match SystematicDeployment::get_all_active_installments_for_today(&mut conn).await {
        Ok(redemptions) => redemptions,
        Err(err) => {
            log_error(LogBuilder::system(&format!(
                "Failed to get active redemptions: {}",
                err
            )));
            return Err(String::from("Failed to get active redemptions"));
        }
    };

    if active_installments.is_empty() {
        log_info(LogBuilder::system("No Active Installments Found"));
        return Ok(());
    };

    // traverse the list and trigger the installments
    for (index, deployment) in active_installments.iter_mut().enumerate() {
        log_info(LogBuilder::system(&format!(
            "Processing Installment for Systematic Deployment _id : {}",
            &deployment.id
        )));

        // check if the installment date is a Holiday
        if is_holiday(deployment.instalment_date).await.unwrap() {
            log_warn(LogBuilder::system(&format!(
                "Skipping Installment for {}, as it is a holiday",
                deployment.id
            )));
            // searching next working day for installment
            let curr_installment_date = deployment.instalment_date;
            let required_installment_date = find_next_working_day(curr_installment_date).await?;
            deployment.instalment_date = required_installment_date;

            let difference = required_installment_date
                .signed_duration_since(curr_installment_date)
                .num_days();
            deployment.redemption_date = deployment
                .redemption_date
                .checked_add_days(chrono::Days::new(difference as u64))
                .unwrap();

            log_info(LogBuilder::system(&format!(
                "Setting next redemption date to {} and \nnext installment date to {} for redemption_id: {}",
                deployment.redemption_date, deployment.instalment_date, deployment.id
            )));

            deployment.update().await;
            if let Err(err) = deployment.commit(&mut conn).await {
                log_error(LogBuilder::system(&format!(
                    "Failed to update installment date for {}",
                    &deployment.id
                )));
            }

            // skip the rest of the loop
            continue;
        }

        // begin the transaction
        start_transaction(&mut conn).await?;

        // post transaction actions
        let auto_buy_enabled = deployment.is_auto_buy_trigger;

        // check if there's any pending installments
        if deployment.pending_installments == 0 {
            log_warn(LogBuilder::system(&format!(
                "Skipping: Installment for {} as there are no pending installments.",
                deployment.id
            )));
            continue;
        }
        // get installment amount
        let amount_to_credit = deployment.instalment_amount;

        deployment.completed_installments += 1;
        deployment.pending_installments -= 1;
        if deployment.pending_installments == 0 {
            deployment.status = "Completed".to_string();
            // deployment.is_active = false; //! check if this is correct
        } else {
            // update the next installment date
            // Redemption Date will be already calculated at time of sell
            let trigger_period_in_days = chrono::Days::new(deployment.sell_trigger_period as u64);
            let tentative_installment_date = deployment
                .redemption_date
                .checked_add_days(trigger_period_in_days)
                .unwrap();
            deployment.instalment_date = find_next_working_day(tentative_installment_date).await?;
        }

        let mut allowed_to_generate_order = false;
        let mut status = "Created".to_string();

        if deployment.last_sell_tracker_id.is_some() {
            let prev_redemption_info =
                DeploymentTracker::get_by_id(&mut conn, deployment.last_sell_tracker_id.as_ref().unwrap()).await?; // safe to unwrap

            // invlid tracker id
            if prev_redemption_info.is_none() {
                log_error(LogBuilder::system(&format!(
                    "Invalid Tracker Id for Deployment: {}",
                    deployment.id
                )));
                return Err("Invalid Previous Tracker Id Found. This is serious. Take Necessary action".to_string());
            }

            // if oder is generated for the Buy
            // generate order for this
            // Check if the previous oder is settled or partially settled
            let previous_installment_status = prev_redemption_info.clone().unwrap().status;
            if previous_installment_status == OrderStatus::Settled.to_string()
                || previous_installment_status == OrderStatus::PartiallySettled.to_string()
            {
                allowed_to_generate_order = auto_buy_enabled;
                status = if allowed_to_generate_order {
                    OrderStatus::Draft.to_string()
                } else {
                    "Created".to_string()
                };
            } else if prev_redemption_info.unwrap().status == OrderStatus::Abandoned.to_string() {
                status = OrderStatus::Abandoned.to_string();
            }
        } else {
            //Don't Do Anything in this case //Send a Mail Also If needed
            log_warn(LogBuilder::system("Last Sell Tracker Id is not set. Skipping..."));
            continue;
        }

        let source_portfoliio_details = PortfolioServiceSTP::get_portfolio_by_id_for_stp(
            &mut conn,
            deployment.source_portfolio_id.clone().unwrap(),
        )
        .await
        .unwrap();
        let source_portfolio_cash_position =
            Portfolio::get_portfolio_cash_position_by_id(&mut conn, source_portfoliio_details.id.to_string())
                .await
                .unwrap()
                .unwrap();

        let destination_portfolio_details = PortfolioServiceSTP::get_portfolio_by_id_for_stp(
            &mut conn,
            deployment.destination_portfolio_id.clone().unwrap(),
        )
        .await
        .unwrap();

        if source_portfolio_cash_position.current_cash_balance < deployment.instalment_amount {
            allowed_to_generate_order = false;
            status = "Created".to_string();
        }

        let rationale = format!(
            "{}Installment No - {}/{}",
            deployment.r#type, deployment.completed_installments, deployment.no_of_installments
        );

        let tracker = DeploymentTracker {
            deployment_setup_id: deployment.id.clone(),
            installment_no: deployment.completed_installments,
            status: status.clone(),
            last_updated_date: Utc::now().naive_local(),
            setup_type: "Buy".to_string(),
            trigger_date: Utc::now().naive_local(),
            triggered_by: if allowed_to_generate_order {
                String::from("System")
            } else {
                String::from("Yet to be trigger")
            },
            rationale: rationale.clone(),
            deployment_type: "Systematic".to_string(),
            previous_tracker_id: deployment.last_sell_tracker_id.clone(),
        };

        let tracker_inserted_id = tracker
            .commit(&mut conn)
            .await
            .map_err(|e| {
                log_error(LogBuilder::system("Failed to insert new Deployment Tracker to DB"));
            })
            .unwrap();

        if allowed_to_generate_order {
            let order_computer = OrderComputer::new(
                app_state.db.clone(),
                app_state.master_db.clone(),
                app_state.tenant_redis_url.clone(),
                app_state.master_redis_url.clone(),
                "System".to_string(),
                amount_to_credit,
            );

            // get the source portfolio and check if there's enough cash
            let mut portfolio = PortfolioServiceSTP::get_portfolio_by_id_for_stp(
                &mut conn,
                deployment.destination_portfolio_id.clone().unwrap(),
            )
            .await
            .unwrap();

            PortfolioServiceSTP::withdraw_capital_with_desc(
                &mut conn,
                deployment.source_portfolio_id.clone().unwrap(),
                amount_to_credit,
                rationale.to_string(),
                false,
            );
            PortfolioServiceSTP::add_capital_with_desc(
                &mut conn,
                deployment.destination_portfolio_id.clone().unwrap(),
                amount_to_credit,
                rationale,
                false,
            );

            let (session_id, orders) = order_computer
                .compute_additional_deployment_for_stp(portfolio.id.to_string(), tracker_inserted_id.clone())
                .await
                .unwrap();
            let save_orders = save_orders(&mut conn, orders, "STP".to_string()).await.unwrap();
        }
        // update the Deployment with new dates
        if let Err(err) = deployment.commit(&mut conn).await {
            log_error(LogBuilder::system(&format!(
                "Failed to update redemption date for {}",
                &deployment.id
            )));
        };
        // commit transaction
        log_info(LogBuilder::system(&format!(
            "Commiting Transaction for Redemption for {}",
            deployment.id
        )));
        commit_transaction(&mut conn).await?;
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use crate::stp::installments::trigger_installments_for_today;
    use actlogica_logs::setting::LogOutput;
    use actlogica_logs::{builder::LogBuilder, log_error, log_info, setting::init_logger};
    use tracing_subscriber::filter::LevelFilter;

    #[tokio::test]
    async fn test_trigger_installments_for_today() {
        dotenv::dotenv().ok();
        if let Err(err) = init_logger("Testing-Redemption", LevelFilter::INFO, LogOutput::StdOut).await {
            log_error(LogBuilder::system("Failed to initialize logger").add_metadata("error", &err.to_string()));
        }
        log_info(LogBuilder::system("Logger service initialized in TestingRedemption"));

        let result = trigger_installments_for_today().await;
        // assert_eq!(result.is_ok(), true);
    }
}
