use axum::{Json, http::StatusCode, response::IntoResponse};
use serde::Serialize;
use serde_json::json;
use utoipa::ToSchema;

pub struct GenericResponse {
    pub message: String,
    pub status: StatusCode,
}

#[derive(Serialize, ToSchema)]
pub struct Response {
    message: String,
}

impl IntoResponse for GenericResponse {
    fn into_response(self) -> axum::response::Response {
        let payload = Response { message: self.message };
        (self.status, Json(payload)).into_response()
    }
}
