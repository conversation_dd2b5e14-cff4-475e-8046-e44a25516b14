use crate::log_actions::Action;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_core_db::{
    connection::pool::{
        deadpool::managed::{Object, Pool},
        Manager,
    },
    schema::{
        client::{Client, ClientBank},
        client_order_entry::TransactionType,
        strategy::{
            strategy_custodian::StrategyCustodians, strategy_model_securities::StrategyModelSecurities, Strategies,
        },
    },
    types::compute_order::StrategyForOrderComputation,
};
use alpha_utils::types::{SecurityDetails, SecurityTypeForPrice};
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use bb8_redis::RedisConnectionManager;
use futures::future::join_all;
use serde::Deserialize;
use serde_json::Value;
use std::sync::Arc;
use tokio::sync::RwLock;
use tracing::{Instrument, Span};

use crate::{
    models::{AppState, TokenClaims},
    oms::error::OmsError,
    types::ClientComputedOrders,
};

#[derive(Deserialize)]
pub struct ComputeOrder {
    pub client_id: String,
    pub capital: f64,
    pub custodian_id: String,
    pub model_id: String,
    pub broker_trading_acc_number: Option<String>,
}

#[tracing::instrument(fields(module = "New-portfolio", event_id = %generate_event_id()), skip_all)]
pub async fn compute_orders_for_new_portfolio_route(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    query: Query<ComputeOrder>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: compute orders for new portfolio route",
            Action::ComputeOrderForNewPortfolioRoute,
        )
        .add_metadata("client_id", &query.client_id)
        .add_metadata("capital", &query.capital.to_string())
        .add_metadata("custodian_id", &query.custodian_id)
        .add_metadata("model_id", &query.model_id),
    );

    let res = compute_orders_for_new_portfolio(
        state.db.clone(),
        state.master_db.clone(),
        state.master_redis_url.clone(),
        query.client_id.clone(),
        query.model_id.clone(),
        query.custodian_id.clone(),
        query.capital,
        tenant.name.clone(),
        query.broker_trading_acc_number.clone(),
    )
    .await
    .map_err(|e| {
        log_error(
            LogBuilder::system(format!("Failed to compute orders for new portfolio route, {:?}", e).as_str())
                .add_metadata("tenant", &tenant.tenant)
                .add_metadata("user_role", &tenant.role.to_string())
                .add_metadata("client_id", &query.client_id)
                .add_metadata("capital", &query.capital.to_string())
                .add_metadata("custodian_id", &query.custodian_id)
                .add_metadata("model_id", &query.model_id),
        );

        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Database error:"),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    log_info(
        LogBuilder::system("RES: Success, compute orders for new portfolio route")
            .add_metadata("client_id", &query.client_id)
            .add_metadata("capital", &query.capital.to_string())
            .add_metadata("custodian_id", &query.custodian_id)
            .add_metadata("model_id", &query.model_id),
    );

    Ok::<(StatusCode, Json<Vec<ClientComputedOrders>>), (StatusCode, Json<Value>)>((StatusCode::CREATED, Json(res)))
}

async fn compute_orders_for_new_portfolio(
    pool: Pool<Manager>,
    master_pool: Pool<Manager>,
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    client_id: String,
    model_id: String,
    custodian_id: String,
    capital: f64,
    created_by: String,
    broker_trading_acc_number: Option<String>,
) -> Result<Vec<ClientComputedOrders>, OmsError> {
    let mut conn = pool.get().await.map_err(|e| {
        log_error(LogBuilder::system(&format!(
            "Failed to get database connection: {:?}",
            e
        )));
        OmsError::DatabaseCallFail
    })?;

    let client = Client::get(&mut conn, client_id.clone())
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to get client: {:?}", e)));
            OmsError::DatabaseCallFail
        })?
        .ok_or_else(|| {
            log_error(LogBuilder::system("Invalid Client Id").add_metadata("client_id", &client_id));
            OmsError::EntityNotFound(String::from("Invalid Client Id"))
        })?;

    let client_bank_account_number = ClientBank::get_client_bank_account_number(&mut conn, &client_id)
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!("Failed to Fetch Client Bank: {:?}", e)));
            OmsError::EntityNotFound(String::from("Failed to Fetch Client Bank"))
        })?
        .unwrap_or_else(|| String::from("Not Available"));

    let strategy = Strategies::get_strategy_for_order_computation_by_model_id(&mut conn, model_id.clone())
        .await
        .map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get strategy for order computation: {:?}",
                e
            )));
            OmsError::DatabaseCallFail
        })?
        .ok_or_else(|| {
            log_error(LogBuilder::system("Invalid Model Id").add_metadata("model_id", &model_id));
            OmsError::EntityNotFound(String::from("Invalid Model Id"))
        })?;

    let strategy_model_securites =
        StrategyModelSecurities::get_for_compute_order_by_model_id(&mut conn, strategy.model_id.clone())
            .await
            .map_err(|e| {
                log_error(LogBuilder::system(&format!(
                    "Failed to get strategy model securities: {:?}",
                    e
                )));
                OmsError::DatabaseCallFail
            })?;

    let strategy_custodian =
        StrategyCustodians::get_by_custodian_id_and_strategy_id(&mut conn, &custodian_id, &strategy.strategy_id)
            .await
            .map_err(|e| {
                log_error(LogBuilder::system(&format!(
                    "Failed to get strategy custodian: {:?}",
                    e
                )));
                OmsError::DatabaseCallFail
            })?
            .ok_or_else(|| {
                log_error(
                    LogBuilder::system("Custodian Id is not in strategy")
                        .add_metadata("custodian_id", &custodian_id)
                        .add_metadata("strategy_id", &strategy.strategy_id),
                );
                OmsError::EntityNotFound(String::from("Custodian Id is not in strategy"))
            })?;

    let computed_orders = Arc::new(RwLock::new(Vec::new()));

    let mut handlers = Vec::new();

    let client = Arc::new(client);
    let strategy = Arc::new(strategy);

    //Compute Order for all the securities in the Model
    for security in strategy_model_securites {
        log_info(
            LogBuilder::system("Computing order for security")
                .add_metadata("security_name", &security.name)
                .add_metadata("security_isin", &security.isin)
                .add_metadata("security_exchange", &security.exchange)
                .add_metadata("security_is_mutual_fund", &security.is_mutual_fund.to_string()),
        );

        let client = client.clone();
        let mut conn = pool.get().await.map_err(|e| {
            log_error(LogBuilder::system(&format!(
                "Failed to get database connection: {:?}",
                e
            )));
            OmsError::DatabaseCallFail
        })?;

        let strategy = strategy.clone();
        let created_by = created_by.clone();
        let computed_orders = computed_orders.clone();
        let broker_trading_acc_number = broker_trading_acc_number.clone();
        let redis_pool = redis_pool.clone();
        let master_pool = master_pool.clone();
        let dp_id = strategy_custodian.dp_id.clone();
        let client_bank_account_number = client_bank_account_number.clone();
        let handler = tokio::spawn(
            async move {
                let client = client.as_ref();
                let strategy = strategy.as_ref();
                match construct_order(
                    computed_orders,
                    &client,
                    security,
                    &strategy,
                    redis_pool,
                    &mut conn,
                    master_pool,
                    capital,
                    created_by,
                    0f64,
                    0f64,
                    broker_trading_acc_number,
                    dp_id,
                    client_bank_account_number,
                )
                .await
                {
                    Ok(()) => {
                        return Ok(());
                    }
                    Err(err) => {
                        log_error(LogBuilder::system(&format!("Failed to construct order: {:?}", err)));
                        return Err(err);
                    }
                }
            }
            .instrument(Span::current()),
        );
        handlers.push(handler);
    }

    //TODO: Check For Error
    let _res = join_all(handlers).await;

    let computed_orders_guard = computed_orders.write().await;
    let orders = (computed_orders_guard).clone();
    Ok(orders)
}

//Construct a Order For a Security
async fn construct_order(
    computed_orders: Arc<RwLock<Vec<ClientComputedOrders>>>,
    client: &Client,
    security: StrategyModelSecurities,
    strategy: &StrategyForOrderComputation,
    redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,
    conn: &mut Object<Manager>,
    master_pool: Pool<Manager>,
    capital: f64,
    created_by: String,
    current_holding: f64,
    portfolio_available_cash: f64,
    broker_trading_acc_number: Option<String>,
    dp_id: String,
    client_bank_account_number: String,
) -> Result<(), String> {
    log_info(LogBuilder::system(&format!(
        "Constructing Order For Security {}",
        security.isin
    )));

    //Get MF Details for this ISIN
    let mut redis_conn = redis_pool.get().await.map_err(|e| {
        log_error(LogBuilder::system(&format!("Failed: Redis connection: {:?}", e)));
        return String::from("Failed to get Redis connection");
    })?;

    let security_details = SecurityDetails::build_with_restricted_stocks(
        &mut redis_conn,
        conn,
        master_pool.clone(),
        security.isin.clone(),
        client.id.clone(),
        if security.is_mutual_fund {
            SecurityTypeForPrice::MutualFund
        } else {
            SecurityTypeForPrice::Equity
        },
        &security.exchange,
    )
    .await?;

    match security_details {
        SecurityDetails::MutualFund(mutual_fund_details) => {
            let latest_price = mutual_fund_details.price;

            if latest_price == 0f64 {
                return Ok(());
            }

            let weighted_investment_amount = (capital * security.weight) / 100f64;

            let investment_quantity = (weighted_investment_amount / latest_price).floor();

            let actual_investment_amount = ((investment_quantity * latest_price) * 100f64).round() / 100f64;

            let mf_folio = "NEW".to_string();

            let mf_buy_sell_type = "FRESH".to_string();

            let mut computed_order_entry = ClientComputedOrders::build_for_new_portfolio_mutual_fund(
                dp_id,
                &client,
                &mutual_fund_details,
                mutual_fund_details.isin.clone(),
                security.exchange.clone(),
                investment_quantity,
                latest_price,
                actual_investment_amount,
                created_by.clone(),
                &strategy,
                TransactionType::Buy,
                current_holding,
                portfolio_available_cash,
            );

            computed_order_entry.mf_folio_number = Some(mf_folio);
            computed_order_entry.mf_client_bank = Some(client_bank_account_number);
            computed_order_entry.mf_buy_sell_type = Some(mf_buy_sell_type);

            computed_order_entry.update_remarks_rationale(
                String::from("Auto Remark: NewCapitalInPortfolio"),
                String::from("Fresh Capital deployment into a new portfolio."),
            );

            if mutual_fund_details.isin != security.isin {
                computed_order_entry.remarks.push_str(" RestrictedStock");
            }

            if let Some(trading_acc_number) = broker_trading_acc_number {
                computed_order_entry.client_trading_account = trading_acc_number;
            }

            let mut computed_orders_guard = computed_orders.write().await;
            computed_orders_guard.push(computed_order_entry);
        }
        SecurityDetails::Stocks(stock_details) | SecurityDetails::ETF(stock_details) => {
            let latest_price = stock_details.get_latest_price_by_exchange(&security.exchange);

            if latest_price == 0f64 {
                return Ok(());
            }

            let weighted_investment_amount = (capital * security.weight) / 100f64;

            let investment_quantity = (weighted_investment_amount / latest_price).floor();

            let actual_investment_amount = ((investment_quantity * latest_price) * 100f64).round() / 100f64;

            let mut computed_order_entry = ClientComputedOrders::build_for_new_portfolio(
                dp_id,
                &client,
                &stock_details,
                stock_details.isin.clone(),
                security.exchange.clone(),
                investment_quantity,
                latest_price,
                actual_investment_amount,
                created_by.clone(),
                &strategy,
                TransactionType::Buy,
                current_holding,
                portfolio_available_cash,
            );

            computed_order_entry.update_remarks_rationale(
                String::from("Auto Remark: NewCapitalInPortfolio"),
                String::from("Fresh Capital deployment into a new portfolio."),
            );

            if stock_details.isin != security.isin {
                computed_order_entry.remarks.push_str(" RestrictedStock");
            }

            if let Some(trading_acc_number) = broker_trading_acc_number {
                computed_order_entry.client_trading_account = trading_acc_number;
            }

            let mut computed_orders_guard = computed_orders.write().await;
            computed_orders_guard.push(computed_order_entry);
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use alpha_core_db::redis::connect_to_redis;

    use super::*;

    #[tokio::test]
    async fn test_new_portfolio_orders() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let matser_pool = alpha_core_db::connection::connect_to_master_data(10).await;

        let tenant_redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(tenant_redis_url);
        let tenant_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(master_redis_url);
        let master_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let model_id = String::from("01be4bf2b9644cd9b6bd67e3c68e51b7");
        let client_id = String::from("01be4bf2b9644cd9b6bd67e3c68e51b7");
        let capital = 43534f64;
        let created_by = String::from("Athul");
        let broker_trading_acc_number = Some(String::from("afsd"));
        let custodian_id = String::from("Cust");

        let orders = compute_orders_for_new_portfolio(
            pool,
            matser_pool,
            tenant_redis,
            model_id,
            custodian_id,
            client_id,
            capital,
            created_by,
            broker_trading_acc_number,
        )
        .await;
    }
}
