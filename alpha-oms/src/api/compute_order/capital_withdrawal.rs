use crate::{log_actions::Action, types::ComputeOrderRsp};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info, log_warn};
use std::sync::Arc;

use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};

use crate::{
    models::{AppState, TokenClaims},
    oms::OrderComputer,
    types::ClientComputedOrders,
};
use serde::Deserialize;
use serde_json::Value;

#[derive(Deserialize)]
pub struct ComputeOrder {
    pub portfolio_id: String,
    pub capital: f64,
    pub complete_exit: bool,
}

#[tracing::instrument(fields(module = "Capital-withdrawal", event_id = %generate_event_id()), skip_all)]
pub async fn compute_orders_for_capital_withdrawals_route(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    query: Query<ComputeOrder>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create parent tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(LogBuilder::user(
        user_id,
        user_name,
        "REQ: Compute order for capital withdrawls route",
        Action::ComputeOrderForCapitalWithdrawalRoute,
    ));
    if !query.complete_exit && query.capital <= 0f64 {
        log_warn(
            LogBuilder::system("Capital should be greater than 0")
                .add_metadata("capital", &query.capital.to_string())
                .add_metadata("user_id", &user_id)
                .add_metadata("user_name", &user_name)
                .add_metadata("portfolio_id", &query.portfolio_id),
        );

        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Capital should be greater than 0"),
        });

        return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
    }

    let capital = if query.complete_exit { 0f64 } else { query.capital };

    let order = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        state.master_redis_url.clone(),
        tenant.name.clone(),
        capital,
    );

    let (session_id, orders): (uuid::Uuid, Vec<ClientComputedOrders>) = order
        .compute_order_for_capital_withdrawal(query.portfolio_id.clone())
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system(format!("Failed to compute order for capital withdrawl routes : {:?}", e).as_str())
                    .add_metadata("portfolio_id", &query.portfolio_id)
                    .add_metadata("created_by", &user_name)
                    .add_metadata("capital", &query.capital.to_string()),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Database error:"),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

    log_info(
        LogBuilder::system("RES: Compute order for capital withdrawls route successfully")
            .add_metadata("portfolio_id", &query.portfolio_id)
            .add_metadata("created_by", &user_name)
            .add_metadata("capital", &query.capital.to_string()),
    );

    Ok::<(StatusCode, Json<ComputeOrderRsp>), (StatusCode, Json<Value>)>((
        StatusCode::CREATED,
        Json(ComputeOrderRsp { orders, session_id }),
    ))
}

#[cfg(test)]
mod tests {

    use super::*;

    #[tokio::test]
    async fn test_compute_order_for_capital_withdrawal() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let matser_pool = alpha_core_db::connection::connect_to_master_data(10).await;

        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(master_redis_url);
        let master_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(redis_url);
        let redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let portfolio_id = String::from("01be4bf2b9644cd9b6bd67e3c68e51b7");
        let capital = 43534f64;
        let created_by = String::from("Athul");

        let order = OrderComputer::new(pool, matser_pool, redis, master_redis, created_by, capital);
        let (_, orders) = order.compute_order_for_capital_withdrawal(portfolio_id).await.unwrap();
        assert_eq!(orders.len(), 39);
    }
}
