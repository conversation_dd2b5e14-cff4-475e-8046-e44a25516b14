use crate::{log_actions::Action, oms::save_orders, types::ComputeOrderRsp};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_core_db::schema::trade_idea::{buy_trade_idea::BuyTradeIdea, sell_trade_idea::SellTradeIdeas};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::sync::Arc;

use crate::{
    models::{
        trade_idea::{BuyTradeIdeaOrderComputation, SellTradeIdeaOrderComputation},
        AppState, TokenClaims,
    },
    oms::OrderComputer,
    types::ClientComputedOrders,
};
use tracing::instrument;

#[instrument(fields(module = "Compute-Order-for-Buy-Trade-Idea", event_id = %generate_event_id()), skip(state, auth))]
pub async fn compute_orders_for_buy_trade_idea(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
    Extension(auth): Extension<TokenClaims>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &auth.name;
    let user_id = &auth.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: compute order for buy trade idea",
            Action::ComputeOrderForBuyTradeIdea,
        )
        .add_metadata("user_role", &auth.role.to_string())
        .add_metadata("id", &id),
    );

    let mut conn = state.db.get().await.map_err(|e| {
        log_error(LogBuilder::system("Failed to get DB connection"));
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Failed to get DB connection: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    //Get the Trade Idea Details
    let buy_trade_idea = BuyTradeIdea::get(&mut conn, id.clone())
        .await
        .map_err(|e| {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error: {:?}", e),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?
        .ok_or_else(|| {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error: Invalid Id"),
            });
            (StatusCode::BAD_REQUEST, Json(error_response))
        })?;

    let order = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        state.master_redis_url.clone(),
        auth.name.clone(),
        0f64,
    );

    let trade_idea_model = BuyTradeIdeaOrderComputation {
        buy_price_from: buy_trade_idea.buy_price_from,
        buy_price_to: buy_trade_idea.buy_price_to,
        change: buy_trade_idea.change,
        exchange: buy_trade_idea.exchange,
        exit_price: buy_trade_idea.exit_price,
        isin: buy_trade_idea.isin,
        model_id: buy_trade_idea.model_id,
    };

    let (session_id, orders) = order.compute_for_buy_trade_idea(trade_idea_model).await.map_err(|e| {
        log_error(
            LogBuilder::system(format!("Failed to compute orders for buy trade idea, {:?}", e).as_str())
                .add_metadata("user_name", &user_name)
                .add_metadata("user_id", &user_id)
                .add_metadata("user_role", &auth.role.to_string())
                .add_metadata("id", &id),
        );

        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Error: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    log_info(
        LogBuilder::system("RES: compute order for buy trade idea")
            .add_metadata("user_role", &auth.role.to_string())
            .add_metadata("id", &id),
    );

    Ok::<(StatusCode, Json<ComputeOrderRsp>), (StatusCode, Json<Value>)>((
        StatusCode::CREATED,
        Json(ComputeOrderRsp { orders, session_id }),
    ))
}

#[instrument(fields(module = "Compute-Order-for-Sell-Trade-Idea", event_id = %generate_event_id()), skip(state, auth))]
pub async fn compute_orders_for_sell_trade_idea(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
    Extension(auth): Extension<TokenClaims>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &auth.name;
    let user_id = &auth.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: compute order for sell trade idea",
            Action::ComputeOrderForSellTradeIdea,
        )
        .add_metadata("user_role", &auth.role.to_string())
        .add_metadata("id", &id),
    );

    let mut conn = state.db.get().await.map_err(|e| {
        log_error(LogBuilder::system("Failed to get DB connection"));
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Failed to get DB connection: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    // Get the Trade Idea Details
    let sell_trade_idea = SellTradeIdeas::get(&mut conn, id.clone())
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system(format!("Failed to fetch sell trade idea, {:?}", e).as_str())
                    .add_metadata("user_name", &user_name)
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_role", &auth.role.to_string())
                    .add_metadata("id", &id),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error: {:?}", e),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?
        .ok_or_else(|| {
            log_error(
                LogBuilder::system("Invalid sell trade idea ID")
                    .add_metadata("user_name", &user_name)
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_role", &auth.role.to_string())
                    .add_metadata("id", &id),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": "Error: Invalid Id",
            });
            (StatusCode::BAD_REQUEST, Json(error_response))
        })?;

    let trade_idea_model = SellTradeIdeaOrderComputation {
        change: sell_trade_idea.change,
        description: sell_trade_idea.description,
        exchange: sell_trade_idea.exchange,
        is_sell_all: sell_trade_idea.is_sell_all,
        isin: sell_trade_idea.isin,
        model_id: sell_trade_idea.model_id,
        security_name: sell_trade_idea.security_name,
        security_type: sell_trade_idea.security_type,
        sell_net_change: sell_trade_idea.sell_net_change,
        sell_price_from: sell_trade_idea.sell_price_from,
        sell_price_to: sell_trade_idea.sell_price_to,
        status: sell_trade_idea.status,
        strategy_id: sell_trade_idea.strategy_id,
        symbol: sell_trade_idea.symbol,
    };

    let order = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        state.master_redis_url.clone(),
        auth.name.clone(),
        0f64,
    );

    let (session_id, orders) = order.compute_for_sell_trade_idea(trade_idea_model).await.map_err(|e| {
        log_error(
            LogBuilder::system(format!("Failed to compute orders for sell trade idea, {:?}", e).as_str())
                .add_metadata("user_name", &user_name)
                .add_metadata("user_id", &user_id)
                .add_metadata("user_role", &auth.role.to_string())
                .add_metadata("id", &id),
        );

        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Error: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    log_info(
        LogBuilder::system("RES: compute order for sell trade idea")
            .add_metadata("user_role", &auth.role.to_string())
            .add_metadata("id", &id),
    );

    Ok::<(StatusCode, Json<ComputeOrderRsp>), (StatusCode, Json<Value>)>((
        StatusCode::CREATED,
        Json(ComputeOrderRsp { orders, session_id }),
    ))
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveTradeIdeaQuery {
    pub idea_type: String,
}

#[instrument(fields(module = "Save-Trade-Idea", event_id = %generate_event_id()), skip_all)]
pub async fn save_trade_idea(
    Path(id): Path<String>,
    State(state): State<Arc<AppState>>,
    Extension(auth): Extension<TokenClaims>,
    Query(query): Query<SaveTradeIdeaQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &auth.name;
    let user_id = &auth.sub;
    let user_role = &auth.role.to_string();

    log_info(
        LogBuilder::user(user_id, user_name, "REQ: Save trade idea", Action::SaveTradeIdea)
            .add_metadata("user_role", &user_role)
            .add_metadata("id", &id),
    );

    let mut conn = state.db.get().await.map_err(|e| {
        log_error(LogBuilder::system("Failed to get DB connection"));
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Failed to get DB connection: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    conn.simple_query("BEGIN TRANSACTION").await.map_err(|e| {
        log_error(LogBuilder::system("Failed to begin transaction"));
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Failed to begin transaction: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    if query.idea_type.to_lowercase() == "buy" {
        //Get the Trade Idea Details
        let buy_trade_idea = BuyTradeIdea::get(&mut conn, id.clone())
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to fetch buy trade idea, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}",e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?
            .ok_or_else(|| {
                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: Invalid Id"),
                });
                (StatusCode::BAD_REQUEST, Json(error_response))
            })?;

        let source_ref_id = buy_trade_idea.id.clone();

        BuyTradeIdea::update_on_create_orders(&mut conn, buy_trade_idea.id.clone(), &auth.name)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to UPDATE buy trade idea on CREATE ORDERS, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}",e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?;

        let order = OrderComputer::new(
            state.db.clone(),
            state.master_db.clone(),
            state.tenant_redis_url.clone(),
            state.master_redis_url.clone(),
            auth.name.clone(),
            0f64,
        );

        let trade_idea_model = BuyTradeIdeaOrderComputation {
            buy_price_from: buy_trade_idea.buy_price_from,
            buy_price_to: buy_trade_idea.buy_price_to,
            change: buy_trade_idea.change,
            exchange: buy_trade_idea.exchange,
            exit_price: buy_trade_idea.exit_price,
            isin: buy_trade_idea.isin,
            model_id: buy_trade_idea.model_id,
        };

        let (_, mut client_orders) = order.compute_for_buy_trade_idea(trade_idea_model).await.map_err(|e| {
            log_error(
                LogBuilder::system(format!("Failed to compute orders for buy trade idea, {:?}", e).as_str())
                    .add_metadata("user_name", &user_name)
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_role", &user_role)
                    .add_metadata("id", &id),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error: {:?}", e),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

        for ord in &mut client_orders {
            ord.source_reference = source_ref_id.clone();
        }

        save_orders(&mut conn, client_orders, auth.name.clone())
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to save trade idea orders, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}", e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?;
    } else {
        //Get the Trade Idea Details
        let sell_trade_idea = SellTradeIdeas::get(&mut conn, id.clone())
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to fetch sell trade idea, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}", e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?
            .ok_or_else(|| {
                log_error(
                    LogBuilder::system("Invalid sell trade idea ID")
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: Invalid Id"),
                });
                (StatusCode::BAD_REQUEST, Json(error_response))
            })?;

        let source_ref_id = sell_trade_idea.id.clone();

        SellTradeIdeas::update_on_create_orders(&mut conn, sell_trade_idea.id.clone(), &auth.name)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to update sell trade idea on create orders, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}", e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?;

        let trade_idea_model = SellTradeIdeaOrderComputation {
            change: sell_trade_idea.change,
            description: sell_trade_idea.description,
            exchange: sell_trade_idea.exchange,
            is_sell_all: sell_trade_idea.is_sell_all,
            isin: sell_trade_idea.isin,
            model_id: sell_trade_idea.model_id,
            security_name: sell_trade_idea.security_name,
            security_type: sell_trade_idea.security_type,
            sell_net_change: sell_trade_idea.sell_net_change,
            sell_price_from: sell_trade_idea.sell_price_from,
            sell_price_to: sell_trade_idea.sell_price_to,
            status: sell_trade_idea.status,
            strategy_id: sell_trade_idea.strategy_id,
            symbol: sell_trade_idea.symbol,
        };

        let order = OrderComputer::new(
            state.db.clone(),
            state.master_db.clone(),
            state.tenant_redis_url.clone(),
            state.master_redis_url.clone(),
            auth.name.clone(),
            0f64,
        );

        let (_, mut client_orders) = order.compute_for_sell_trade_idea(trade_idea_model).await.map_err(|e| {
            log_error(
                LogBuilder::system(format!("Failed to compute orders for sell trade idea, {:?}", e).as_str())
                    .add_metadata("user_name", &user_name)
                    .add_metadata("user_id", &user_id)
                    .add_metadata("user_role", &user_role)
                    .add_metadata("id", &id),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Error: {:?}", e),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

        for ord in &mut client_orders {
            ord.source_reference = source_ref_id.clone();
        }

        save_orders(&mut conn, client_orders, auth.name.clone())
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed to save trade idea orders, {:?}", e).as_str())
                        .add_metadata("user_name", &user_name)
                        .add_metadata("user_id", &user_id)
                        .add_metadata("user_role", &user_role)
                        .add_metadata("id", &id),
                );

                let error_response = serde_json::json!({
                    "status": "error",
                    "message": format!("Error: {:?}", e),
                });
                (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
            })?;
    }

    conn.simple_query("COMMIT").await.map_err(|e| {
        log_error(
            LogBuilder::system(format!("Failed to commit COE, {:?}", e).as_str())
                .add_metadata("user_name", &user_name)
                .add_metadata("user_id", &user_id)
                .add_metadata("user_role", &user_role)
                .add_metadata("id", &id),
        );
        let error_response = serde_json::json!({
            "status": "error",
            "message": format!("Error: {:?}", e),
        });
        (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
    })?;

    let response = serde_json::json!({
        "status": "success",
        "message": "Saved Successfully"
    });
    log_info(
        LogBuilder::system("RES: Trade idea saved successfully")
            .add_metadata("user_id", &user_id)
            .add_metadata("user_name", &user_name)
            .add_metadata("user_role", &user_role)
            .add_metadata("id", &id),
    );
    Ok::<(StatusCode, Json<Value>), (StatusCode, Json<Value>)>((StatusCode::CREATED, Json(response)))
}

#[cfg(test)]
mod tests {
    use alpha_core_db::redis::connect_to_redis;
    use tracing_test::traced_test;

    use super::*;

    #[tokio::test]
    #[traced_test]
    async fn test_compute_buy_trade_idea() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(30).await;
        let master_pool = alpha_core_db::connection::connect_to_master_data(10).await;
        let mut conn = pool.get().await.unwrap();

        let redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(redis_url);
        let redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(master_redis_url);
        let master_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let id = String::from("8e3acc0de50b4422b10f2f1f86993946");
        let capital = 434355f64;
        let created_by = String::from("Athul");

        //Get the Trade Idea Details
        let buy_trade_idea = BuyTradeIdea::get(&mut conn, id).await.unwrap().unwrap();

        let order = OrderComputer::new(pool.clone(), master_pool.clone(), redis, master_redis, created_by, 0f64);

        let trade_idea_model = BuyTradeIdeaOrderComputation {
            buy_price_from: buy_trade_idea.buy_price_from,
            buy_price_to: buy_trade_idea.buy_price_to,
            change: buy_trade_idea.change,
            exchange: buy_trade_idea.exchange,
            exit_price: buy_trade_idea.exit_price,
            isin: buy_trade_idea.isin,
            model_id: buy_trade_idea.model_id,
        };

        let orders = order.compute_for_buy_trade_idea(trade_idea_model).await.unwrap();
    }

    #[tokio::test]
    #[traced_test]
    async fn test_compute_sell_trade_idea() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(30).await;
        let master_pool = alpha_core_db::connection::connect_to_master_data(10).await;
        let mut conn = pool.get().await.unwrap();
        let redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(redis_url);
        let redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(master_redis_url);
        let master_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let id = String::from("cc31d7b44c9346e5bfee93ea6db29c13");
        let capital = 434355f64;
        let created_by = String::from("Athul");

        //Get the Trade Idea Details
        let sell_trade_idea = SellTradeIdeas::get(&mut conn, id).await.unwrap().unwrap();

        let order = OrderComputer::new(pool.clone(), master_pool.clone(), redis, master_redis, created_by, 0f64);

        let trade_idea_model = SellTradeIdeaOrderComputation {
            change: sell_trade_idea.change,
            description: sell_trade_idea.description,
            exchange: sell_trade_idea.exchange,
            is_sell_all: sell_trade_idea.is_sell_all,
            isin: sell_trade_idea.isin,
            model_id: sell_trade_idea.model_id,
            security_name: sell_trade_idea.security_name,
            security_type: sell_trade_idea.security_type,
            sell_net_change: sell_trade_idea.sell_net_change,
            sell_price_from: sell_trade_idea.sell_price_from,
            sell_price_to: sell_trade_idea.sell_price_to,
            status: sell_trade_idea.status,
            strategy_id: sell_trade_idea.strategy_id,
            symbol: sell_trade_idea.symbol,
        };

        let orders = order.compute_for_sell_trade_idea(trade_idea_model).await.unwrap();
    }
}
