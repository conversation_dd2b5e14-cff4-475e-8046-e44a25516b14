use crate::{
    error::ApiError,
    models::{
        AppState,
        recon_result::{MatchStatusQuery, ReconRunResultRsp},
    },
};
use alpha_core_db::tenant_clickhouse::reconciliation::{
    recon_result::{ReconR<PERSON>ult, ReconResultFilter},
    recon_run::ReconProjectRuns,
};
use axum::{
    Json,
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
};
use chrono::NaiveDate;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Debug, ToSchema, Clone, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReconResultReqParam {
    #[schema(value_type = Option<String>, example = "Client")]
    client_name: Option<String>,

    #[schema(value_type = Option<String>, example = "RC-101")]
    client_code: Option<String>,

    #[schema(value_type = Option<String>, example = "HDFC")]
    security_name: Option<String>,

    #[schema(value_type = Option<String>, example = "INE1212122")]
    isin: Option<String>,

    #[schema(value_type = Option<String>, example = "AVL")]
    symbol: Option<String>,

    recon_match_status: Option<MatchStatusQuery>,

    #[schema(value_type = u64, example = "10")]
    limit: u64,

    #[schema(value_type = u64, example = "0")]
    offset: u64,
}

#[derive(Debug, ToSchema, Clone, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct GetReconResultRsp {
    results: Vec<ReconRunResultRsp>,

    #[schema(value_type = u64, example = "100")]
    total_records: u64,

    #[schema(value_type = u64, example = "10")]
    current_offset: u64,
}

#[utoipa::path(
    get,
    tag = "Recon Result",
    path = "/recon-result/get-recon-result/{run_id}",
    params(
        ("run_id" = String, Path, description = "Run ID"),
        ("offset" = String, Query, description = "Offset of the pagination"),
        ("limit" = String, Query, description = "Total number of records in a single request"),
        ("securityName" = Option<String>, Query, description = "Security name to filter"),
        ("isin" = Option<String>, Query, description = "ISIN to filter"),
        ("symbol" = Option<String>, Query, description = "Symbol to filter"),
        ("clientCode" = Option<String>, Query, description = "ClientCode to filter"),
        ("clientName" = Option<String>, Query, description = "ClientName to filter"),
        ("reconMatchStatus" = Option<MatchStatusQuery>, Query, description = "Match status filter")
    ),
    responses(
        (status = 200, description = "Run details found", body = GetReconResultRsp),
        (status = 404, description = "Run details not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_recon_result(
    State(state): State<Arc<AppState>>,
    Query(query): Query<ReconResultReqParam>,
    Path(run_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let filter = ReconResultFilter {
        isin: query.isin,
        security_name: query.security_name,
        client_code: query.client_code,
        client_name: query.client_name,
        symbol: query.symbol,
        match_status: query.recon_match_status.map(Into::into),
    };

    let (recon_result) =
        ReconResult::get_filtered(&state.tenant_clickhouse, &run_id, filter, query.limit, query.offset).await;

    match recon_result {
        Ok((total_records, recon_res)) => {
            let results = recon_res.into_iter().map(Into::into).collect();

            return Ok((
                StatusCode::OK,
                Json(GetReconResultRsp {
                    results,
                    total_records,
                    current_offset: query.offset,
                }),
            )
                .into_response());
        }
        Err(e) => Err(ApiError::InternalError {
            message: format!("Failed to retrieve recon result, please try again"),
            status: false,
        }),
    }
}
