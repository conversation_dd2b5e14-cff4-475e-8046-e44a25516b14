use crate::{
    api::project,
    error::ApiError,
    models::{AppState, recon_project::ReconProjectRsp, recon_project_runs::ReconProjectRunsRsp},
};
use alpha_core_db::tenant_clickhouse::reconciliation::{project::ReconProject, recon_run::ReconProjectRuns};
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct GetAllRunsForProjectRsp {
    project: ReconProjectRsp,
    runs: Vec<ReconProjectRunsRsp>,
}

#[utoipa::path(
    get,
    tag = "Recon runs",
    path = "/recon-run/get-all/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "Runs found", body = GetAllRunsForProjectRsp),
        (status = 404, description = "Runs not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_all_runs(
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<Json<GetAllRunsForProjectRsp>, impl IntoResponse> {
    let project_details = match ReconProject::get_by_id(&state.tenant_clickhouse, &project_id).await {
        Ok(project) => project,
        Err(err) => {
            return Err(ApiError::NotFound {
                message: "Requested project not found".to_string(),
                status: false,
            });
        }
    };

    let runs = ReconProjectRuns::get_all(&state.tenant_clickhouse, &project_id).await;
    match runs {
        Ok(runs) => Ok(Json(GetAllRunsForProjectRsp {
            project: project_details.into(),
            runs: runs.into_iter().map(Into::into).collect(),
        })),
        Err(e) => Err(ApiError::InternalError {
            message: format!("Failed to get all recon runs. Please try again"),
            status: false,
        }),
    }
}
