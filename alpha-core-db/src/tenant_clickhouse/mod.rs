pub mod pre_trade_compliance_report;
pub mod reconciliation;

use clickhouse::Client;

pub fn connect_tenant_clickhouse_client() -> Client {
    let url = std::env::var("TENANT_CLICKHOUSE_URL").expect("Failed to load TENANT_CLICKHOUSE_URL from env");
    let user_name =
        std::env::var("TENANT_CLICKHOUSE_USER_NAME").expect("Failed to load TENANT_CLICKHOUSE_USER_NAME from env");
    let password =
        std::env::var("TENANT_CLICKHOUSE_PASSWORD").expect("Failed to load TENANT_CLICKHOUSE_PASSWORD from env");
    let database_name =
        std::env::var("TENANT_CLICKHOUSE_DB_NAME").expect("Failed to load TENANT_CLICKHOUSE_DB_NAME from env");

    Client::default()
        .with_url(url)
        .with_user(user_name)
        .with_password(password)
        .with_database(database_name)
}

pub fn connect_tenant_recon_clickhouse_client() -> Client {
    let url = std::env::var("TENANT_CLICKHOUSE_URL").expect("Failed to load TENANT_CLICKHOUSE_URL from env");
    let user_name =
        std::env::var("TENANT_CLICKHOUSE_USER_NAME").expect("Failed to load TENANT_CLICKHOUSE_USER_NAME from env");
    let password =
        std::env::var("TENANT_CLICKHOUSE_PASSWORD").expect("Failed to load TENANT_CLICKHOUSE_PASSWORD from env");
    
    let database_name = std::env::var("TENANT_CLICKHOUSE_RECON_DB_NAME")
        .expect("Failed to load TENANT_CLICKHOUSE_RECON_DB_NAME from env");

    Client::default()
        .with_url(url)
        .with_user(user_name)
        .with_password(password)
        .with_database(database_name)
}
