use alpha_core_db::tenant_clickhouse::reconciliation::client_snapshot::{ClientReportRow, ClientSnapshot};
use chrono::NaiveDate;
use clickhouse::Client;
use csv::Reader;
use csv::ReaderBuilder;
use std::io::Cursor;

/*
*    The CSV columns are:
*    0: HOLDING DATE
*    1: CODE
*    2: NAME
*    3: ASSET CLASS (ignored)
*    4: SYMBOLCODE (symbol)
*    5: SECURITYNAME (security_name)
*    6: ISIN (isin)
*    7: POSITION (position)
*    8: QUANTITY (holdings)
*    9: UNIT COST (unit_cost)
*    10: TOTAL COST (total_cost)
*    11: UNIT PRICE (ignored)
*    12: ACCRUED INCOME (accrued_income)
*    13: MARKET VALUE (market_value)
*/

pub async fn process_client_report(
    file_path: &str,
    project_id: &str,
    client: &Client,
) -> Result<(), Box<dyn std::error::Error>> {
    let file = std::fs::File::open(file_path)?;
    let reader = std::io::BufReader::new(file);
    let mut reader = Reader::from_reader(reader);

    let mut client_records = Vec::new();

    let start = std::time::Instant::now(); // Stopwatch start

    for result in reader.deserialize() {
        let mut record: ClientReportRow = result?;

        // println!("{:?}", row);
        if record.symbol == "CASH" {
            record.isin = "CASH".to_string();
        }
        client_records.push(record);
    }

    let duration = start.elapsed();
    println!("Record found: {:?}", client_records.len());
    println!("Time taken to read file: {:.2?}", duration);

    let start = std::time::Instant::now(); // Stopwatch start
    // match ClientSnapshot::insert_batch(client, client_records, project_id.to_string()).await {
    //     Ok(_) => {
    //         let duration = start.elapsed();
    //         match ClientSnapshot::prepare_client_snapshot_table(client, project_id).await {
    //             Ok(_) => {},
    //             Err(e) => {
    //                 println!("❌ Error preparing client snapshot table: {}", e);
    //             }
    //         }
    //         println!("Time taken to insert rows: {:.2?}", duration);
    //     }
    //     Err(e) => {
    //         println!("❌ Error inserting rows into database: {}", e);
    //     }
    // }
    Ok(())
}

pub fn validate_csv_headers(data: &[u8]) -> Result<(), String> {
    let cursor = Cursor::new(data);
    let mut rdr = ReaderBuilder::new().has_headers(true).from_reader(cursor);

    let headers = rdr
        .headers()
        .map_err(|e| format!("Failed to read CSV headers: {}", e))?;

    let required_headers = [
        "HOLDING DATE",
        "CODE",
        "NAME",
        "SYMBOLCODE",
        "SYMBOLNAME",
        "ISIN",
        "POSITION",
        "QUANTITY",
        "UNIT COST",
        "TOTAL COST",
        "ACCRUED INCOME",
        "MARKET VALUE",
    ];

    for required in &required_headers {
        if !headers.iter().any(|h| h == *required) {
            println!("Missing required column: {}", required);
            return Err(format!("Missing required column: {}", required));
        }
    }

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use alpha_core_db::tenant_clickhouse::connect_tenant_recon_clickhouse_client;
    use dotenv::dotenv;
    use std::time::Instant;

    #[tokio::test]
    async fn test_process_client_report() {
        dotenv().ok();
        let client = connect_tenant_recon_clickhouse_client();
        let file_path = "src/test-data/client_report.csv";

        let start = Instant::now();
        let result = process_client_report(file_path, "2", &client).await;
        let duration = start.elapsed();

        println!("⏱️ Time taken to process: {:.2?}", duration);

        result.unwrap();
    }

    #[tokio::test]
    async fn test_validate_csv_headers() {
        dotenv().ok();
        let file_path = "src/test-data/client_report.csv";
        let file = std::fs::read(file_path).unwrap();
        let result = validate_csv_headers(&file);
        assert!(result.is_ok());
    }
}
