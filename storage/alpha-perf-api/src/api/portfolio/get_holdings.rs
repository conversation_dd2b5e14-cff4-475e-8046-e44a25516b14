use std::{fs, sync::Arc};

use alpha_storage_engine::storage::{portfolio::PortfolioWithStartDate, StorageWriter};
use axum::{
    extract::{Path, Query, State},
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use chrono::{Days, NaiveDate, Utc};
use csv::Writer;
use rkyv::Deserialize;
use rust_decimal::Decimal;
use serde::Serialize;
use serde_json::{json, Value};
use utoipa::ToSchema;

use crate::AppState;

#[derive(serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetHoldings {
    pub from_date: NaiveDate,
    pub to_date: NaiveDate,
}

#[derive(serde::Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetHoldingsBody {
    pub portfolios: Vec<String>,
}

#[derive(<PERSON>ial<PERSON>, <PERSON>lone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct Investment {
    pub portfolio_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    pub name: String,
    pub isin: String,
    #[schema(value_type = f64, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub holdings: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub average_price: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub current_price: Decimal,
}

#[derive(Serialize, Clone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioHoldings {
    holdings: Vec<Investment>,
}

#[utoipa::path(
    get,
    tag = "Portfolio",
    path = "/portfolio/holdings/{portfolio_id}",
    responses(
        (status = 200, description = "Portfolio Holdings", body = PortfolioHoldings),
        (status = NOT_FOUND, description = "Portfolio Holdings was not found")
    ),
    params(
        ("fromDate" = String, Query, description = "From Date filter for Portfolio Holdings query"),
        ("toDate" = String, Query, description = "To Date filter for Portfolio Holdings query")
    )
)]

pub async fn get_holdings(
    State(state): State<Arc<AppState>>,
    Query(payload): Query<GetHoldings>,
    Path(portfolio_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let mut start_date = payload.from_date;
    let end_date = payload.to_date;

    let mut holdings: Vec<Investment> = Vec::new();

    while start_date < end_date {
        let mut prefix: String = format!(
            "{}-{}-",
            alpha_storage_engine::storage::DbKeyPrefix::PortfolioInvestment,
            portfolio_id
        );
        prefix.push_str(&start_date.to_string());

        let investments = state.storage_engine.db.get(prefix);
        match investments {
            Ok(investments) => {
                if let Some(investments) = investments {
                    let archived = unsafe {
                        rkyv::archived_root::<Vec<alpha_storage_engine::states::investment::Investment>>(
                            &investments[..],
                        )
                    };

                    let mut investments_des: Result<
                        Vec<alpha_storage_engine::states::investment::Investment>,
                        std::convert::Infallible,
                    > = archived.deserialize(&mut rkyv::Infallible);
                    if let Ok(inv) = &mut investments_des {
                        for invs in inv {
                            let inv = Investment {
                                portfolio_id: invs.portfolio_id.clone(),
                                date: invs.date,
                                isin: invs.isin.clone(),
                                holdings: invs.holdings,
                                market_value: invs.market_value,
                                name: invs.name.clone(),
                                average_price: invs.average_price,
                                current_price: invs.current_price,
                            };
                            holdings.push(inv);
                        }
                    }
                }
            }
            Err(aum) => {
                let res = json!({
                    "status": "error",
                    "message": format!("Failed to Get AUM From Data")
                });

                return Err((StatusCode::BAD_REQUEST, Json(res)));
            }
        }

        start_date = start_date.checked_add_days(Days::new(1)).unwrap();
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in holdings.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./holdings.csv", buff).unwrap();

    Ok::<(StatusCode, Json<PortfolioHoldings>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(PortfolioHoldings { holdings }),
    ))
}

pub async fn get_holdings_for_portfolio(
    State(state): State<Arc<AppState>>,
    Json(payload): Json<GetHoldingsBody>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage = StorageWriter::new(state.storage_engine.db.clone());
    let portfolios = storage.get_all_portfolio_with_start_date();
    let portfolios: Vec<PortfolioWithStartDate> = portfolios
        .into_iter()
        .filter(|p| payload.portfolios.contains(&p.portfolio_id))
        .collect();

    let mut holdings: Vec<Investment> = Vec::new();

    for portfolio in portfolios {
        let mut start_date = portfolio.start_date;
        let end_date = Utc::now().date_naive();
        while start_date < end_date {
            let mut prefix: String = format!(
                "{}-{}-",
                alpha_storage_engine::storage::DbKeyPrefix::PortfolioInvestment,
                portfolio.portfolio_id
            );
            prefix.push_str(&start_date.to_string());

            let investments = state.storage_engine.db.get(prefix);
            match investments {
                Ok(investments) => {
                    if let Some(investments) = investments {
                        let archived = unsafe {
                            rkyv::archived_root::<Vec<alpha_storage_engine::states::investment::Investment>>(
                                &investments[..],
                            )
                        };

                        let mut investments_des: Result<
                            Vec<alpha_storage_engine::states::investment::Investment>,
                            std::convert::Infallible,
                        > = archived.deserialize(&mut rkyv::Infallible);
                        if let Ok(inv) = &mut investments_des {
                            for invs in inv {
                                let inv = Investment {
                                    portfolio_id: invs.portfolio_id.clone(),
                                    date: invs.date,
                                    isin: invs.isin.clone(),
                                    holdings: invs.holdings,
                                    market_value: invs.market_value,
                                    name: invs.name.clone(),
                                    average_price: invs.average_price,
                                    current_price: invs.current_price,
                                };
                                holdings.push(inv);
                            }
                        }
                    }
                }
                Err(aum) => {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get AUM From Data")
                    });

                    return Err((StatusCode::BAD_REQUEST, Json(res)));
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in holdings.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./holdings.csv", buff).unwrap();

    Ok::<(StatusCode, Json<PortfolioHoldings>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(PortfolioHoldings { holdings }),
    ))
}

#[derive(Serialize, Clone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct InvestmentAllPortfolios {
    pub portfolio_id: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub date: NaiveDate,
    pub name: String,
    pub isin: String,
    #[schema(value_type = f64, example = "0")]
    pub market_value: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub holdings: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub average_price: Decimal,
    #[schema(value_type = f64, example = "0")]
    pub current_price: Decimal,
}

#[derive(Serialize, Clone, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct AllPortfolioHoldings {
    holdings: Vec<InvestmentAllPortfolios>,
}

pub async fn get_all_portfolio_holdings(
    State(state): State<Arc<AppState>>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let storage = StorageWriter::new(state.storage_engine.db.clone());
    let portfolios = storage.get_all_portfolio_with_start_date();
    let mut holdings: Vec<InvestmentAllPortfolios> = Vec::new();

    for portfolio in portfolios {
        let mut start_date = portfolio.start_date;
        let end_date = Utc::now().date_naive();

        while start_date < end_date {
            let mut prefix: String = format!(
                "{}-{}-",
                alpha_storage_engine::storage::DbKeyPrefix::PortfolioInvestment,
                portfolio.portfolio_id
            );
            prefix.push_str(&start_date.to_string());

            let investments = state.storage_engine.db.get(prefix);
            match investments {
                Ok(investments) => {
                    if let Some(investments) = investments {
                        let archived = unsafe {
                            rkyv::archived_root::<Vec<alpha_storage_engine::states::investment::Investment>>(
                                &investments[..],
                            )
                        };

                        let mut investments_des: Result<
                            Vec<alpha_storage_engine::states::investment::Investment>,
                            std::convert::Infallible,
                        > = archived.deserialize(&mut rkyv::Infallible);
                        if let Ok(inv) = &mut investments_des {
                            for invs in inv {
                                let inv = InvestmentAllPortfolios {
                                    portfolio_id: portfolio.portfolio_id.clone(),
                                    date: invs.date,
                                    isin: invs.isin.clone(),
                                    holdings: invs.holdings,
                                    market_value: invs.market_value,
                                    name: invs.name.clone(),
                                    average_price: invs.average_price,
                                    current_price: invs.current_price,
                                };
                                holdings.push(inv);
                            }
                        }
                    }
                }
                Err(aum) => {
                    let res = json!({
                        "status": "error",
                        "message": format!("Failed to Get AUM From Data")
                    });

                    return Err((StatusCode::BAD_REQUEST, Json(res)));
                }
            }

            start_date = start_date.checked_add_days(Days::new(1)).unwrap();
        }
    }

    let mut wtr = Writer::from_writer(vec![]);

    for row in holdings.clone() {
        wtr.serialize(row).unwrap();
    }

    let buff = wtr
        .into_inner()
        .map_err(|e| "Failed to Serialise Row into Bytes".to_string())
        .unwrap();

    fs::write("./holdings.csv", buff).unwrap();

    Ok::<(StatusCode, Json<AllPortfolioHoldings>), (StatusCode, Json<Value>)>((
        StatusCode::ACCEPTED,
        Json(AllPortfolioHoldings { holdings }),
    ))
}
