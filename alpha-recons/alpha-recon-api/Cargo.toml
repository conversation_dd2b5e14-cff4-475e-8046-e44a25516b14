[package]
name = "alpha-recon-api"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { workspace = true }
dotenv = { workspace = true }
alpha-utils = { path = "../../alpha-utils" }
alpha-core-db = { path = "../../alpha-core-db" }
recon-util = { path = "../recon-util" }
serde = { workspace = true }
serde_json = { workspace = true }
chrono = { workspace = true }
axum = { version = "0.8.0", features = ["macros", "multipart"] }
axum-extra = { version = "0.10.1", features = ["cookie"] }
jsonwebtoken = "9.3.0"
tracing = { workspace = true }
tracing-subscriber = { workspace = true }
tracing-test = { workspace = true }
futures-util = "0.3.30"
futures = "0.3.30"
tower-http = { version = "0.6.1", features = ["cors"] }
redis = { workspace = true }
bb8 = { workspace = true }
bb8-redis = { workspace = true }
uuid = { workspace = true }
deadpool-redis = { workspace = true }
deadpool = { workspace = true }
anyhow = { workspace = true }
csv = { workspace = true }
clickhouse = { workspace = true }
utoipa = { version = "5.3.1", features = ["axum_extras"] }
utoipa-swagger-ui = { version = "9.0.1", features = ["axum"] }
actlogica_logs = { workspace = true }
time.workspace = true
minio = "0.3.0"
thiserror = { workspace = true }
