use crate::{log_actions::Action, oms::deviation::DeviationSession};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

use crate::{
    models::{AppState, TokenClaims},
    types::CustomisedOrder,
};

#[derive(Serialize, Deserialize)]
pub struct GetDeviationComputedOrdersQuery {
    /// Unique Deviation Id
    id: String,

    /// Seq from which next entries are required
    seq: u64,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_deviation_computed_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(query): <PERSON><PERSON><GetDeviationComputedOrdersQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Get Deviation Computed Order",
            Action::GetDeviationComputedOrder,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id)
        .add_metadata("seq", &query.seq.to_string()),
    );

    let deviation_session = DeviationSession::build(query.id.clone(), state.clone());

    let start_seq = query.seq;
    let end_seq = start_seq + 99;

    let orders = match deviation_session.get_orders(start_seq, end_seq).await {
        Ok(orders) => orders,
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": "Failed to fetch orders from Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    log_info(
        LogBuilder::system("RES: Successfully fetched and deserialized orders")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("total_orders", &orders.len().to_string()),
    );

    Ok::<(StatusCode, axum::Json<Vec<CustomisedOrder>>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(orders),
    ))
}
