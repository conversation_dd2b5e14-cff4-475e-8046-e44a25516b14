use crate::models::{AppState, recon_project::ReconProjectRsp};
use alpha_core_db::tenant_clickhouse::reconciliation::project::{ProjectResponse, ReconProject};
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use utoipa::{ToSchema, openapi::example};

#[derive(ToSchema, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct GetProjectRsp {
    project: ReconProjectRsp,
}

#[utoipa::path(
    get,
    tag = "Project",
    path = "/project/get/{id}",
    params(
        ("id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "Project found", body = GetProjectRsp),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_project_by_id(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(id): Path<String>,
) -> Result<Json<GetProjectRsp>, impl IntoResponse> {
    let result = ReconProject::get_by_id(&state.tenant_clickhouse, &id).await;

    match result {
        Ok(project) => Ok(Json(GetProjectRsp {
            project: project.into(),
        })),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
