use std::sync::Arc;

use actlogica_logs::{builder::Log<PERSON>uilder, log_error, log_info};
use alpha_core_db::schema::oms::PortfolioDetailsForOms;
use alpha_utils::constants::RedisKey;
use anyhow::{anyhow, Context};
use csv::Writer;
use deadpool_redis::Connection;
use redis::AsyncCommands;

use crate::{
    models::AppState,
    types::{deviation::ModelDeviationReport, CustomisedOrder},
};

pub mod deviation;
pub mod deviation_compute_order;

pub mod validate_deviation_orders;

pub struct ReportsToBeUpdated {
    pub report_id: u64,
    pub selected: bool,
}

/// Give all details of a deviation session
pub struct DeviationSession {
    deviation_id: String,
    app_state: Arc<AppState>,
}

impl DeviationSession {
    const EXPIRY: i64 = 5 * 60;

    pub fn build(deviation_id: String, app_state: Arc<AppState>) -> Self {
        Self {
            deviation_id,
            app_state,
        }
    }

    async fn key_expired(&self, key: &str, conn: &mut Connection) -> anyhow::Result<bool> {
        let key_exists: bool = conn.exists(&key).await.map_err(|err| {
            log_error(
                LogBuilder::system("Failed to check Redis key existence")
                    .add_metadata("key", &key)
                    .add_metadata("error", &format!("{:?}", err)),
            );
            anyhow::anyhow!("Failed to check Redis key existence")
        })?;

        if !key_exists {
            log_error(LogBuilder::system("Session Expired: Key does not exist in Redis").add_metadata("key", &key));
            return Ok(true);
        }

        return Ok(false);
    }

    pub async fn get_all_reports(&self) -> anyhow::Result<Vec<ModelDeviationReport>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationReport.to_string(),
            self.deviation_id.clone()
        );

        let reports_string: Vec<String> = redis_conn.zrange(key.clone(), 0, -1).await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: get report - {}", err)));
            anyhow::anyhow!("Failed to get report, please try again")
        })?;

        let mut reports: Vec<ModelDeviationReport> = Vec::new();

        for report in reports_string {
            let report = serde_json::from_str::<ModelDeviationReport>(&report).map_err(|_err| {
                log_error(LogBuilder::system("Failed: deserialize report"));
                anyhow::anyhow!("Report deserialisation failed")
            })?;
            reports.push(report);
        }

        return Ok(reports);
    }

    pub async fn get_reports_in_csv(&self) -> anyhow::Result<Vec<u8>> {
        let reports = self.get_all_reports().await?;

        let mut wtr = Writer::from_writer(vec![]);

        for report in reports {
            wtr.serialize(report).map(|_err| {
                log_error(LogBuilder::system("Failed: contruct report CSV"));
                anyhow::anyhow!("Failed to contruct CSV")
            })?;
        }

        let buff = wtr.into_inner().context("Failed to construct CSV")?;

        Ok(buff)
    }

    pub async fn get_reports(&self, start_seq: u64, end_seq: u64) -> anyhow::Result<Vec<ModelDeviationReport>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationReport.to_string(),
            self.deviation_id.clone()
        );

        let items_str: Vec<String> = match redis_conn
            .zrangebyscore::<String, u64, u64, Vec<String>>(key.clone(), start_seq, end_seq)
            .await
        {
            Ok(items) => items,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: fetch report from Redis: {:?}", err).as_str(),
                ));
                return Err(anyhow::anyhow!("failed to fetch reports after updation"));
            }
        };

        let mut reports: Vec<ModelDeviationReport> = Vec::new();

        let reports: Vec<ModelDeviationReport> = items_str
            .into_iter()
            .map(|item| {
                serde_json::from_str(&item).map_err(|_| {
                    log_error(LogBuilder::system("Failed: deserialize data").add_metadata("str_item", &item));
                    anyhow::anyhow!("deserialise data")
                })
            })
            .collect::<Result<_, _>>()?;

        Ok(reports)
    }

    pub async fn get_reports_by_ids(&self, report_ids: &[u64]) -> anyhow::Result<Vec<ModelDeviationReport>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationReport.to_string(),
            self.deviation_id.clone()
        );

        log_info(LogBuilder::system("Fetching reports in bulk from Redis"));

        let mut pipeline = deadpool_redis::redis::pipe();

        for report_id in report_ids {
            pipeline.zrangebyscore(&key, *report_id, *report_id);
        }

        let results = pipeline
            .query_async::<Vec<Option<Vec<String>>>>(&mut redis_conn)
            .await
            .map_err(|_| anyhow::anyhow!("failed to fetch reports"))?;

        let reports: Vec<ModelDeviationReport> = results
            .into_iter()
            .map(|opt_vec| {
                let vec = opt_vec.ok_or_else(|| anyhow!("Missing report entry"))?;
                let json_str = vec.get(0).ok_or_else(|| anyhow!("Empty report vector"))?;
                serde_json::from_str(json_str).map_err(|e| anyhow!("JSON parse error: {}", e))
            })
            .collect::<anyhow::Result<_>>()?;

        Ok(reports)
    }

    pub async fn update_reports(&self, reports_to_update: Vec<ReportsToBeUpdated>) -> anyhow::Result<()> {
        //We need to fetch the portfolio details of the selected reports and store in the cache
        //And update selected field of the report in the cache
        let mut portfolio_to_fetch = Vec::with_capacity(reports_to_update.len());
        let mut report_ids = Vec::with_capacity(reports_to_update.len());

        for report in &reports_to_update {
            report_ids.push(report.report_id);
        }

        let mut reports = self.get_reports_by_ids(&report_ids).await?;

        let mut updated_reports = Vec::with_capacity(reports.len());

        for (idx, mut report) in reports.into_iter().enumerate() {
            report.selected = reports_to_update[idx].selected;
            let report_ser = serde_json::to_vec(&report).map_err(|e| anyhow::anyhow!("failed to update report"))?;

            portfolio_to_fetch.push(report.portfolio_id);

            updated_reports.push((report.id, report_ser));
        }

        let mut db_conn = match self.app_state.db.get().await {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(format!("Failed: DB connection: {:?}", err).as_str()));
                return Err(anyhow::anyhow!("Failed to get data from db"));
            }
        };

        let portfolios = match PortfolioDetailsForOms::get_bulk(&mut db_conn, &portfolio_to_fetch).await {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: Get portfolio info: {:?}", err).as_str(),
                ));
                return Err(anyhow::anyhow!("failed to get portfolio from db"));
            }
        };

        let ser_portfolios: Vec<(Vec<u8>, String)> = portfolios
            .iter()
            .map(|p| {
                serde_json::to_vec(p)
                    .map(|serialized| (serialized, p.portfolio.id.clone()))
                    .map_err(|e| {
                        log_error(LogBuilder::system("Failed to serialize portfolio"));
                        anyhow::anyhow!("failed to serialize portfolio: {}", e)
                    })
            })
            .collect::<anyhow::Result<_, _>>()?;

        self.update_reports_in_bulk_and_add_portfolio_details(updated_reports, &ser_portfolios)
            .await?;

        Ok(())
    }

    async fn update_reports_in_bulk_and_add_portfolio_details(
        &self,
        updated_reports: Vec<(u64, Vec<u8>)>,
        portfolios: &Vec<(Vec<u8>, String)>,
    ) -> anyhow::Result<()> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationReport.to_string(),
            self.deviation_id.clone()
        );

        let mut pipeline = deadpool_redis::redis::pipe();
        pipeline.atomic();

        for (report_id, serialized_report) in updated_reports {
            //First Remove the entry to avoid duplication
            pipeline.zrembyscore(&key, report_id, report_id);
            pipeline.zadd(&key, serialized_report, report_id);
        }

        pipeline.expire(&key, Self::EXPIRY);

        for (portfolio, portfolio_id) in portfolios {
            let key = format!("{}:{}", RedisKey::DeviationPortfolios, portfolio_id);
            pipeline.set_ex(key, portfolio, Self::EXPIRY as u64);
        }

        pipeline
            .query_async::<deadpool_redis::redis::Value>(&mut redis_conn)
            .await
            .map_err(|err| {
                log_error(
                    LogBuilder::system("Failed: fetch reports in bulk from redis pipeline")
                        .add_metadata("error", err.to_string().as_str()),
                );
                return anyhow::anyhow!("failed to save update reports");
            })?;

        Ok(())
    }

    pub async fn delete_order(&self, seq: &Vec<u64>) -> anyhow::Result<()> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!("{}:{}", RedisKey::DeviationOrders.to_string(), self.deviation_id);

        let mut pipe = deadpool_redis::redis::pipe();
        pipe.atomic();

        for score in seq {
            pipe.zrembyscore(&key, *score, *score);
        }

        let delete_counts: Vec<i64> = pipe.query_async(&mut redis_conn).await.map_err(|err| {
            log_error(
                LogBuilder::system("Failed to delete orders from Redis pipeline")
                    .add_metadata("deviation_id", &self.deviation_id)
                    .add_metadata("error", &format!("{:?}", err)),
            );
            anyhow::anyhow!("Failed to delete orders from Redis")
        })?;

        if delete_counts.is_empty() {
            let expired = self.key_expired(&key, &mut redis_conn).await?;
            if expired {
                return Err(anyhow::anyhow!("redis key expired"));
            }
        }

        log_info(
            LogBuilder::system("Successfully deleted deviation orders")
                .add_metadata("deviation_id", &self.deviation_id)
                .add_metadata("deleted_count", &delete_counts.len().to_string()),
        );

        Ok(())
    }

    pub async fn get_orders(&self, start_seq: u64, end_seq: u64) -> anyhow::Result<Vec<CustomisedOrder>> {
        let mut redis_conn = self.app_state.tenant_redis_url.get().await.map_err(|err| {
            log_error(LogBuilder::system(&format!("Failed: Redis Connection - {}", err)));
            anyhow::anyhow!("Failed to get Redis connection: {}", err)
        })?;

        let key = format!(
            "{}:{}",
            RedisKey::DeviationOrders.to_string(),
            self.deviation_id.clone()
        );

        let items_str: Vec<String> = match redis_conn
            .zrangebyscore::<String, u64, u64, Vec<String>>(key.clone(), start_seq, end_seq)
            .await
        {
            Ok(items) => items,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: fetch report from Redis: {:?}", err).as_str(),
                ));
                return Err(anyhow::anyhow!("failed to fetch reports after updation"));
            }
        };

        let mut reports: Vec<CustomisedOrder> = Vec::new();

        let reports: Vec<CustomisedOrder> = items_str
            .into_iter()
            .map(|item| {
                serde_json::from_str(&item).map_err(|_| {
                    log_error(LogBuilder::system("Failed: deserialize data").add_metadata("str_item", &item));
                    anyhow::anyhow!("deserialise data")
                })
            })
            .collect::<Result<_, _>>()?;

        Ok(reports)
    }

    pub async fn save_orders_to_cache(&self, orders: &Vec<CustomisedOrder>) -> anyhow::Result<()> {
        let mut redis_conn = match self.app_state.tenant_redis_url.get().await {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: Redis connection: {:?}", err).as_str(),
                ));
                return Err(anyhow::anyhow!("failed to get save please try again"));
            }
        };

        let key = format!(
            "{}:{}",
            RedisKey::DeviationOrders.to_string(),
            self.deviation_id.to_string(),
        );

        // Create a pipeline
        let mut pipe = deadpool_redis::redis::pipe();

        // Delete existing key and add all orders in pipeline
        pipe.del(&key);

        for order in orders.iter() {
            pipe.zadd(&key, serde_json::to_vec(&order).unwrap(), order.id);
        }

        // Set expiration time in the same pipeline
        pipe.expire(&key, 5 * 60);

        // Execute the pipeline atomically
        pipe.query_async::<deadpool_redis::redis::Value>(&mut redis_conn)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system(format!("Failed: Save report to cache. REASON :{}", e).as_str())
                        .add_metadata("session_id", &self.deviation_id),
                );

                return anyhow::anyhow!("Failed to save into cache");
            })?;

        Ok(())
    }

    pub async fn get_and_save_all_portfolios(&self) -> anyhow::Result<()> {
        let reports = self.get_all_reports().await?;
        let portfolios_to_fetch: Vec<String> = reports.iter().map(|r| r.portfolio_id.clone()).collect();

        let mut db_conn = match self.app_state.db.get().await {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(format!("Failed: DB connection: {:?}", err).as_str()));
                return Err(anyhow::anyhow!("Failed to get data from db"));
            }
        };

        let portfolios = match PortfolioDetailsForOms::get_bulk(&mut db_conn, &portfolios_to_fetch).await {
            Ok(conn) => conn,
            Err(err) => {
                log_error(LogBuilder::system(
                    format!("Failed: Get portfolio info: {:?}", err).as_str(),
                ));
                return Err(anyhow::anyhow!("failed to get portfolio from db"));
            }
        };

        let ser_portfolios: Vec<(Vec<u8>, String)> = portfolios
            .iter()
            .map(|p| {
                serde_json::to_vec(p)
                    .map(|serialized| (serialized, p.portfolio.id.clone()))
                    .map_err(|e| {
                        log_error(LogBuilder::system("Failed to serialize portfolio"));
                        anyhow::anyhow!("failed to serialize portfolio: {}", e)
                    })
            })
            .collect::<anyhow::Result<_, _>>()?;

        self.update_reports_in_bulk_and_add_portfolio_details(Default::default(), &ser_portfolios)
            .await?;

        Ok(())
    }
}
