pub mod portfolio_broker;
pub mod portfolio_custodian;
pub mod portfolio_service;

use chrono::{DateTime, NaiveDate, NaiveDateTime, Utc};
use deadpool::managed::Object;
use portfolio_custodian::PortfolioCustodian;
use serde::{Deserialize, Serialize};
use std::str::FromStr;

use tiberius::{error::Error, ExecuteResult, Query, Row};

use crate::{connection::pool::Manager, error::DatabaseError, types::compute_order::StrategyForOrderComputation};

use super::{
    cash_ledger::PortfolioCashLedger, client_order_entry::TransactionType, investment::Investments,
    trade_order_unsettled::TradeOrderUnsettledAmounts,
};

#[derive(Debug, Serialize, Deserialize, Default, Clone)]
#[serde(rename_all = "camelCase")]
pub struct PortfolioCashPosition {
    pub current_cash_balance: f64,
    pub available_cash_balance: f64,
    pub submitted_buy_amount: f64,
    pub submitted_sell_amount: f64,
    pub held_buy_amount: f64,
    pub held_sell_amount: f64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioWithCash {
    pub portfolio: Portfolio,
    pub cash: PortfolioCashPosition,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioForTradeIdea {
    pub portfolio: Portfolio,
    pub cash: PortfolioCashPosition,
    pub dp_id: String,
    pub broker_trading_acc_number: Option<String>,
}

impl PortfolioForTradeIdea {
    pub fn from_row(row: &Row) -> Self {
        Self {
            cash: PortfolioCashPosition::from_row(row),
            dp_id: row.get::<&str, _>("DpID").unwrap().to_string(),
            broker_trading_acc_number: row.get::<&str, _>("TradingAccountNumber").map(String::from),
            portfolio: Portfolio::from_row(row),
        }
    }
}

/// This will be used for Sell Trade Idea
/// THe Current Holding will be the isin for which trade idea is happening
#[derive(Debug, Serialize, Deserialize)]
pub struct PortfolioForRebalancing {
    pub portfolio: Portfolio,
    pub cash: PortfolioCashPosition,
    pub dp_id: String,
    pub broker_trading_acc_number: Option<String>,
    pub current_holding: f64,
}

impl PortfolioForRebalancing {
    pub fn from_row(row: &Row) -> Self {
        Self {
            cash: PortfolioCashPosition::from_row(row),
            dp_id: row.get::<&str, _>("DpID").unwrap().to_string(),
            broker_trading_acc_number: row.get::<&str, _>("TradingAccountNumber").map(String::from),
            portfolio: Portfolio::from_row(row),
            current_holding: row.get::<f64, _>("CurrentHolding").unwrap(),
        }
    }
}

impl PortfolioWithCash {
    pub fn from_row(row: &Row) -> Self {
        Self {
            portfolio: Portfolio::from_row(row),
            cash: PortfolioCashPosition::from_row(row),
        }
    }
}

impl PortfolioCashPosition {
    pub fn from_row(row: &Row) -> Self {
        Self {
            current_cash_balance: row.get::<f64, _>("CurrentCashBalance").unwrap(),
            available_cash_balance: row.get::<f64, _>("AvailableCashBalance").unwrap(),
            submitted_buy_amount: row.get::<f64, _>("SubmittedBuyAmount").unwrap(),
            submitted_sell_amount: row.get::<f64, _>("SubmittedSellAmount").unwrap(),
            held_buy_amount: row.get::<f64, _>("HeldBuyAmount").unwrap(),
            held_sell_amount: row.get::<f64, _>("HeldSellAmount").unwrap(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Eq, Clone)]
pub enum PortfolioType {
    Discretionary,
    NonDiscretionary,
    Advisory,
}

#[derive(Debug, Serialize, Deserialize, PartialEq, Clone)]
pub enum AccountStatus {
    Draft,
    Active,
    InActive,
    Frozen,
    Closed,
    Exiting,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum ModeOfOperation {
    Pool,
    Individual,
}

#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum TradingMode {
    Pool,
    Individual,
}

#[derive(Debug, Serialize, Deserialize)]
pub enum FundSettlementMode {
    Pool,
    Individual,
}

impl FromStr for PortfolioType {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_ref() {
            "discretionary" => Ok(PortfolioType::Discretionary),
            "nondiscretionary" => Ok(PortfolioType::NonDiscretionary),
            "advisory" => Ok(PortfolioType::Advisory),
            _ => Err(()),
        }
    }
}

impl FromStr for AccountStatus {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "draft" => Ok(AccountStatus::Draft),
            "active" => Ok(AccountStatus::Active),
            "inactive" => Ok(AccountStatus::InActive),
            "frozen" => Ok(AccountStatus::Frozen),
            "closed" => Ok(AccountStatus::Closed),
            "exiting" => Ok(AccountStatus::Exiting),
            _ => Err(()),
        }
    }
}

impl FromStr for ModeOfOperation {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_ref() {
            "pool" => Ok(ModeOfOperation::Pool),
            "individual" => Ok(ModeOfOperation::Individual),
            _ => Err(()),
        }
    }
}

impl FromStr for TradingMode {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_ref() {
            "pool" => Ok(TradingMode::Pool),
            "individual" => Ok(TradingMode::Individual),
            _ => Err(()),
        }
    }
}

impl FromStr for FundSettlementMode {
    type Err = ();
    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_ref() {
            "pool" => Ok(FundSettlementMode::Pool),
            "individual" => Ok(FundSettlementMode::Individual),
            _ => Err(()),
        }
    }
}

pub struct PortfolioForDeviations {
    pub client_id: String,
    pub client_name: String,
    pub client_code: String,
    pub portfolio_id: String,
    pub client_strategy_code: String,
    pub portfolio_name: String,
    pub current_cash_balance: f64,
    pub market_value: f64,
    pub account_status: AccountStatus,
}

impl PortfolioForDeviations {
    pub fn from_row(row: &Row) -> Self {
        Self {
            client_code: row.get::<&str, _>("ClientCode").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            client_name: row.get::<&str, _>("ClientName").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            current_cash_balance: row.get::<f64, _>("CurrentCashBalance").unwrap(),
            portfolio_id: row.get::<&str, _>("PortfolioId").unwrap().to_string(),
            portfolio_name: row.get::<&str, _>("PortfolioName").unwrap().to_string(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
            account_status: AccountStatus::from_str(row.get::<&str, _>("AccountStatus").unwrap()).unwrap(),
        }
    }
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct PortfolioForPerformanceEngine {
    pub id: String,
    pub client_id: String,
    pub model_id: String,
    pub strategy_id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Portfolio {
    pub id: String,
    pub client_id: String,
    pub model_id: String,
    pub client_strategy_code: String,
    pub custodian_portfolio_code: String,
    pub fa_account_no: Option<String>,
    pub name: String,
    pub portfolio_type: PortfolioType,
    pub start_date: DateTime<Utc>,
    pub total_capital: f64,
    pub invested_capital: f64,
    pub market_value: f64,
    pub realised_gain_loss: f64,
    pub unrealised_gain_loss: f64,
    pub annual_return_irr: f64,
    pub annual_return_irr_unrealised: f64,
    pub twrr_since_inception: f64,
    pub annual_performance_twrr: f64,
    pub current_cash_balance: f64,
    pub unsettled_buy_order_amount: f64,
    pub unsettled_cash_amount: f64,
    pub account_status: AccountStatus,
    pub mode_of_operation: ModeOfOperation,
    pub trading_mode: TradingMode,
    pub fund_settlement_mode: FundSettlementMode,
    pub stock_settlement_mode: Option<String>,
    pub poa_on_bank: bool,
    pub poa_on_demat: bool,
    pub poa_on_mf: bool,
    pub account_name: Option<String>,
    pub last_updated_date: NaiveDateTime,
}

impl PortfolioForPerformanceEngine {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            strategy_id: row.get::<&str, _>("StrategyId").unwrap().to_string(),
        }
    }
}

impl Portfolio {
    pub fn from_row(row: &Row) -> Self {
        Self {
            id: row.get::<&str, _>("Id").unwrap().to_string(),
            client_id: row.get::<&str, _>("ClientId").unwrap().to_string(),
            model_id: row.get::<&str, _>("ModelId").unwrap().to_string(),
            client_strategy_code: row.get::<&str, _>("ClientStrategyCode").unwrap().to_string(),
            custodian_portfolio_code: row.get::<&str, _>("CustodianPortfolioCode").unwrap().to_string(),
            fa_account_no: row.get::<&str, _>("FAAccountNo").map(String::from),
            name: row.get::<&str, _>("Name").unwrap().to_string(),
            portfolio_type: PortfolioType::from_str(row.get::<&str, _>("PortfolioType").unwrap()).unwrap(),
            start_date: row.get::<DateTime<Utc>, _>("StartDate").unwrap(),
            total_capital: row.get::<f64, _>("TotalCapital").unwrap(),
            invested_capital: row.get::<f64, _>("InvestedCapital").unwrap(),
            market_value: row.get::<f64, _>("MarketValue").unwrap(),
            realised_gain_loss: row.get::<f64, _>("RealisedGainLoss").unwrap(),
            unrealised_gain_loss: row.get::<f64, _>("UnRealisedGainLoss").unwrap(),
            annual_return_irr: row.get::<f64, _>("AnnualReturnIrr").unwrap(),
            annual_return_irr_unrealised: row.get::<f64, _>("AnnualReturnIrrUnrealised").unwrap(),
            twrr_since_inception: row.get::<f64, _>("TwrrSinceInception").unwrap(),
            annual_performance_twrr: row.get::<f64, _>("AnnualPerformanceTwrr").unwrap(),
            current_cash_balance: row.get::<f64, _>("CurrentCashBalance").unwrap(),
            unsettled_buy_order_amount: row.get::<f64, _>("UnsettledBuyOrderAmount").unwrap(),
            unsettled_cash_amount: row.get::<f64, _>("UnsettledCashAmount").unwrap(),
            account_status: AccountStatus::from_str(row.get::<&str, _>("AccountStatus").unwrap()).unwrap(),
            mode_of_operation: ModeOfOperation::from_str(row.get::<&str, _>("ModeOfOperation").unwrap()).unwrap(),
            trading_mode: TradingMode::from_str(row.get::<&str, _>("TradingMode").unwrap()).unwrap(),
            fund_settlement_mode: FundSettlementMode::from_str(row.get::<&str, _>("FundSettlementMode").unwrap())
                .unwrap(),
            stock_settlement_mode: row.get::<&str, _>("StockSettlementMode").map(String::from),
            poa_on_bank: row.get::<bool, _>("POAOnBank").unwrap(),
            poa_on_demat: row.get::<bool, _>("POAOnDemat").unwrap(),
            poa_on_mf: row.get::<bool, _>("POAOnMF").unwrap(),
            account_name: row.get::<&str, _>("AccountName").map(String::from),
            last_updated_date: row.get::<NaiveDateTime, _>("LastUpdatedDate").unwrap(),
        }
    }

    pub async fn get(conn: &mut Object<Manager>, id: String) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            Portfolios
        WHERE
            Id = @P1

        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_all(conn: &mut Object<Manager>) -> Result<Vec<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            Portfolios

        "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();
        Ok(rows)
    }

    pub async fn get_portfolio_with_current_cash(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Option<PortfolioWithCash>, DatabaseError> {
        let query = r#"
            SELECT
                p.*,
                CashBalance.*
            FROM
                Portfolios p
                CROSS APPLY dbo.GetPortfolioCashBalance(p.Id) CashBalance
            Where p.Id = @P1

        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(PortfolioWithCash::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_all_for_performance_engine(
        conn: &mut Object<Manager>,
    ) -> Result<Vec<PortfolioForPerformanceEngine>, DatabaseError> {
        let query = r#"
                SELECT
                    p.Id,
                    p.ModelId,
                    p.ClientId,
                    s.Id as StrategyId
                FROM
                    Portfolios p
                JOIN
                    StrategyModels sm ON sm.Id = p.ModelId
                JOIN
                    Strategies s ON s.Id = sm.StrategyId
            "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<PortfolioForPerformanceEngine> =
            rows_iter.iter().map(PortfolioForPerformanceEngine::from_row).collect();

        Ok(rows)
    }

    pub async fn get_for_v2(conn: &mut Object<Manager>) -> Result<Vec<String>, DatabaseError> {
        let query = r#"
            SELECT
                PortfolioId,
                MIN(TransactionDate) as FirstTransactionDate
            FROM
                PortfolioCapitalRegisters
            WHERE PortfolioId Is NOT NULL
            GROUP BY
                PortfolioId
            ORDER BY
                FirstTransactionDate;
        "#;

        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let mut result = Vec::new();

        for row in rows_iter {
            let portfolio_id = row.get::<&str, _>("PortfolioId").unwrap().to_string();
            result.push(portfolio_id);
        }

        Ok(result)
    }

    pub async fn get_by_id_for_performance_engine(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Option<Self>, DatabaseError> {
        let query = r#"
        SELECT
            *
        FROM
            Portfolios
        WHERE
            Id = @P1

        "#;

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| return DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(Self::from_row(&rows_iter.unwrap())))
        }
    }

    // fetch PMOperatingExpense data, return vector of rows
    pub async fn get_pm_operating_expense_details(conn: &mut Object<Manager>) -> Result<Vec<Row>, DatabaseError> {
        // todo: add data fields for query
        let query: &str = r#"
        SELECT
            c.ClientCode,
            ptf.Id as PortfolioId
            -- agreement_date,
            -- other fields to be filled
        FROM
            Clients c
        JOIN
            Portfolios ptf ON ptf.ClientId = c.Id
        "#;
        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        Ok(rows_iter)
    }

    pub async fn get_pm_pool_acc_master_details(conn: &mut Object<Manager>) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT
            c.DpId           as POOL_DP_ID,
            c.Name           as POOL_DP_NAME,
            s.Name           as POOL_NAME,
            c.DpId + sc.DpId as POOL_BOID
        FROM
            Strategies s
        JOIN
            StrategyCustodians sc on s.Id=sc.StrategyId
        JOIN
            Custodians c on c.Id=sc.CustodianId
        "#;
        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;
        Ok(rows_iter)
    }

    pub async fn get_pm_master_details(conn: &mut Object<Manager>) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT
            PMName as PM_NAME,
            PMPan as PM_PAN,
            PMPOName as PM_PO_PAN,
            PMPOPan as PM_PO_NAME,
            PMPODateOfJoin as PM_PO_DOJ,
            PMSebiRegno as PM_SEBI_REG_NO,
            PMCOPan as PM_CO_PAN,
            PMCOName as PM_CO_NAME,
            PONismCertNo as PO_NISM_CERTIFICATE_NO,
            PONismCertDate as PO_NISM_CERTIFIC_DATE,
            FIURegNo as FIU_REG_NO,
            KRARegNo as KRA_REG_NO,
            CERSAIRegno as CERSAI_REG_NO,
            ScoreRegNo as SCORE_REG_NO,
            WebLink as WEBLINK,
            FoSystemName as FO_SYSTEM_NAME,
            BPSystemName as BO_SYSTEM_NAME,
            PMSurrenderDate as PM_SURRENDER_DATE,
            PMNetWorth as PM_NET_WORTH,
            PMNetworthDate as PM_NET_WORTH_DATE
        FROM
            PortfolioManagerMasters
        "#;
        let rows_iter = conn
            .query(query, &[])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;
        Ok(rows_iter)
    }

    pub async fn get_pms_inspection_details(
        conn: &mut Object<Manager>,
        from_date: NaiveDate,
        to_date: NaiveDate,
    ) -> Result<Vec<Row>, DatabaseError> {
        let query: &str = r#"
        SELECT
            it.isin as ISIN,
            it.TransactionDate as TRANSACTION_DATE,
            i.AssetClass AS ASSET_CLASS,
            i.SecurityType AS SECURITY_TYPE,
            i.Symbol AS SYMBOL
        FROM
            InvestmentTransactions it
        INNER JOIN
            Investments i on it.InvestmentId = i.Id
        WHERE
            CAST(it.TransactionDate AS DATE) BETWEEN @P1 AND @P2
        GROUP BY
            it.Isin, it.TransactionDate, i.AssetClass, i.SecurityType, i.Symbol
        "#;
        let rows_iter = conn
            .query(query, &[&from_date, &to_date])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;
        Ok(rows_iter)
    }

    pub async fn update_on_performance_engine(
        &self,
        conn: &mut Object<Manager>,
    ) -> Result<ExecuteResult, DatabaseError> {
        let mut query = Query::new(
            "
            UPDATE
                Portfolios
            SET
                TotalCapital = @P1,
                InvestedCapital = @P2,
                MarketValue = @P3,
                CurrentCashBalance = @P4,
                RealisedGainLoss = @P5,
                UnRealisedGainLoss = @P6,
                LastUpdatedDate = @P7
            WHERE
                Id = @P8
        ",
        );

        query.bind(self.total_capital);
        query.bind(self.invested_capital);
        query.bind(self.market_value);
        query.bind(self.current_cash_balance);
        query.bind(self.realised_gain_loss);
        query.bind(self.unrealised_gain_loss);
        query.bind(self.last_updated_date);
        query.bind(self.id.clone());

        query.execute(conn).await.map_err(|e| DatabaseError::QueryError((e)))
    }

    pub async fn get_by_model_id(conn: &mut Object<Manager>, id: String) -> Result<Vec<Portfolio>, DatabaseError> {
        let query = "
            SELECT
                *
            FROM
                Portfolios
            WHERE
                ModelId = @P1
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<Self> = rows_iter.iter().map(Self::from_row).collect();

        Ok(rows)
    }

    pub async fn get_portfolio_in_model_id_for_trade_idea(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Vec<PortfolioForTradeIdea>, DatabaseError> {
        let query = "
        SELECT
                p.*,
                CashBalance.*,
                sc.DpID,
                cb.TradingAccountNumber
            FROM
                Portfolios p
                CROSS APPLY dbo.GetPortfolioCashBalance(p.Id) CashBalance
                LEFT JOIN ClientCustodians cc ON p.Id = cc.PortfolioId
                INNER JOIN StrategyModels sm ON p.ModelId = sm.Id
                INNER JOIN StrategyCustodians sc ON sc.CustodianId = cc.CustodianId
                    AND sc.StrategyId = sm.StrategyId
                LEFT JOIN ClientBrokers cb ON p.Id = cb.PortfolioId
            WHERE
                p.ModelId = @P1
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<PortfolioForTradeIdea> = rows_iter.iter().map(PortfolioForTradeIdea::from_row).collect();

        Ok(rows)
    }

    pub async fn get_by_model_id_for_rebalancing(
        conn: &mut Object<Manager>,
        id: String,
        isin: String,
    ) -> Result<Vec<PortfolioForRebalancing>, DatabaseError> {
        let query = "
                SELECT
                p.*,
                CashBalance.*,
                sc.DpID,
                cb.TradingAccountNumber,
                inv.CurrentHolding
            FROM
                Portfolios p
                CROSS APPLY dbo.GetPortfolioCashBalance(p.Id) CashBalance
                JOIN  Investments inv ON inv.PortfolioId = p.Id
                LEFT JOIN ClientCustodians cc ON p.Id = cc.PortfolioId
                LEFT JOIN StrategyModels sm ON p.ModelId = sm.Id
                LEFT JOIN StrategyCustodians sc ON sc.CustodianId = cc.CustodianId
                    AND sc.StrategyId = sm.StrategyId
                LEFT JOIN ClientBrokers cb ON p.Id = cb.PortfolioId
            WHERE
                p.ModelId = @P1
                     AND
                inv.Isin = @P2


            SELECT
                *
            FROM
                Portfolios p
            JOIN
                 Investments inv ON inv.PortfolioId = p.Id
            WHERE
                p.ModelId = @P1
                AND
                inv.Isin = @P2
        ";

        let rows_iter = conn
            .query(query, &[&id, &isin])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                return DatabaseError::QueryError((e));
            })?;

        let rows: Vec<PortfolioForRebalancing> = rows_iter.iter().map(PortfolioForRebalancing::from_row).collect();

        Ok(rows)
    }

    pub async fn get_portfolios_for_deviation(
        conn: &mut Object<Manager>,
        model_id: &str,
    ) -> Result<Vec<PortfolioForDeviations>, DatabaseError> {
        let query = "
            SELECT
                    CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS ClientName,
                    c.Id as ClientId,
                    c.ClientCode,
                    p.Id as PortfolioId,
                    p.ClientStrategyCode,
                    p.Name as PortfolioName,
                    p.CurrentCashBalance,
                    p.MarketValue,
                    p.AccountStatus
            FROM
                    Clients c join Portfolios p
                        on c.Id = p.ClientId
            WHERE
                    p.ModelId = @P1

        ";

        let rows_iter = conn
            .query(query, &[&model_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let rows: Vec<PortfolioForDeviations> = rows_iter.iter().map(PortfolioForDeviations::from_row).collect();

        Ok(rows)
    }

    pub async fn get_portfolios_by_csc_for_deviation(
        conn: &mut Object<Manager>,
        client_strategy_codes: &[String],
        model_id: &str,
    ) -> Result<Vec<PortfolioForDeviations>, DatabaseError> {
        let formatted_codes = client_strategy_codes
            .iter()
            .map(|s| format!("'{}'", s.replace('\'', "''")))
            .collect::<Vec<String>>()
            .join(",");

        let query = format!(
            "
            SELECT
                    CONCAT(c.FirstName, ' ', COALESCE(c.MiddleName, ''), ' ', c.LastName) AS ClientName,
                    c.Id as ClientId,
                    c.ClientCode,
                    p.Id as PortfolioId,
                    p.ClientStrategyCode,
                    p.Name as PortfolioName,
                    p.CurrentCashBalance,
                    p.MarketValue,
                    p.AccountStatus
            FROM
                    Clients c join Portfolios p
                        on c.Id = p.ClientId
            WHERE
                    p.ModelId = @P1
                    AND
                    p.ClientStrategyCode In ({})

        ",
            formatted_codes
        );

        let rows_iter = conn
            .query(query, &[&model_id])
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?
            .into_first_result()
            .await
            .map_err(|e| {
                println!("{:?}", e);
                e
            })?;

        let rows: Vec<PortfolioForDeviations> = rows_iter.iter().map(PortfolioForDeviations::from_row).collect();

        Ok(rows)
    }

    pub async fn get_portfolio_cash_position_by_id(
        conn: &mut Object<Manager>,
        id: String,
    ) -> Result<Option<PortfolioCashPosition>, DatabaseError> {
        let query = "
                SELECT * FROM dbo.GetPortfolioCashBalance(@P1)
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_row()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;

        if rows_iter.is_none() {
            Ok(None)
        } else {
            Ok(Some(PortfolioCashPosition::from_row(&rows_iter.unwrap())))
        }
    }

    pub async fn get_investment_holdings_by_portfolio_id(
        conn: &mut Object<Manager>,
        id: &str,
    ) -> Result<Vec<Investments>, DatabaseError> {
        let query = "
                SELECT * FROM dbo.GetPortfolioInvestmentHoldings(@P1)
        ";

        let rows_iter = conn
            .query(query, &[&id])
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?
            .into_first_result()
            .await
            .map_err(|e| DatabaseError::QueryError((e)))?;
        let res: Vec<Investments> = rows_iter.iter().map(Investments::from_row).collect();

        Ok(res)
    }
}

#[cfg(test)]
mod tests {

    use crate::{
        connection::{connect_to_master_data, connect_to_mssql},
        schema::portfolio::Portfolio,
    };

    #[tokio::test]
    async fn get_portfolio_with_cash() {
        dotenv::dotenv().ok();
        let pool = connect_to_mssql(1).await;
        let mut pool_conn = pool.get().await.unwrap();
        let id = String::from("6dbd642bc96948e9b5bc750967495f95");
        let portfolio_with_cash = Portfolio::get_portfolio_with_current_cash(&mut pool_conn, id)
            .await
            .unwrap();

        println!("{:?}", portfolio_with_cash);
    }
}
