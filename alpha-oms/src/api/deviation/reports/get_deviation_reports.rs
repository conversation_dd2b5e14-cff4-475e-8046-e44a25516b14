use crate::{
    api::deviation::utils::fetch_reports_in_bulk,
    log_actions::Action,
    oms::deviation::{DeviationSession, ReportsToBeUpdated},
};
use actlogica_logs::{builder::Log<PERSON>uilder, generator::generate_event_id, log_error, log_info, log_warn};
use alpha_core_db::schema::oms::PortfolioDetailsForOms;
use alpha_utils::constants::RedisKey;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{collections::HashSet, sync::Arc};

use crate::{
    models::{AppState, TokenClaims},
    types::deviation::ModelDeviationReport,
};

use super::update_reports_in_bulk_and_add_portfolio_details;

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportToChange {
    report_id: u64,
    select: bool,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GetDeviationReportsQuery {
    id: String,
    seq: u64,
    changes: Vec<ReportToChange>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn get_deviation_reports(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(query): Json<GetDeviationReportsQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Get Deviation reports",
            Action::GetDeviationReports,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id)
        .add_metadata("seq", &query.seq.to_string()),
    );

    let deviation_session = DeviationSession::build(query.id.clone(), state.clone());
    let reports_to_update = query
        .changes
        .iter()
        .map(|change| ReportsToBeUpdated {
            report_id: change.report_id,
            selected: change.select,
        })
        .collect();

    match deviation_session.update_reports(reports_to_update).await {
        Ok(_) => {}
        Err(err) => {
            let error_response = json!({
                "status": "error",
                "message": "Failed to update reports in Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }

    //After updation get next sequence of reports

    let start_seq = query.seq;
    let end_seq = start_seq + 99;

    let reports = match deviation_session.get_reports(start_seq, end_seq).await {
        Ok(reports) => reports,
        Err(err) => {
            let error_response = json!({
                "status": "error",
                "message": err.to_string(),
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "RES: Successfully fetched and updated Deviation reports",
            Action::BuildDeviationReport,
        )
        .add_metadata("deviation_id", &query.id)
        .add_metadata("seq", &query.seq.to_string())
        .add_metadata("reports_count", &reports.len().to_string()),
    );

    Ok::<(StatusCode, axum::Json<Vec<ModelDeviationReport>>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(reports),
    ))
}
