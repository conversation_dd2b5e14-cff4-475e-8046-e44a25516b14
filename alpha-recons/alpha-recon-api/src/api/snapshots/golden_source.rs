use crate::models::AppState;
use alpha_core_db::minio::download_file;
use alpha_core_db::tenant_clickhouse::reconciliation::project::ReconProject;
use axum::body::Bytes;
use axum::{
    Json,
    extract::{Path, State},
    http::{StatusCode, HeaderMap, header, HeaderValue},
    response::IntoResponse,
};
use minio::s3::{Client, types::S3Api};
use std::sync::Arc;
use utoipa::ToSchema;

#[utoipa::path(
    get,
    tag = "Snapshot",
    path = "/snapshots/download-golden-source-csv/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    responses(
        (status = 200, description = "File downloaded successfully"),
        (status = 404, description = "Project not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn download_golden_source_csv(
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let project = ReconProject::get_golden_source_url(&state.tenant_clickhouse, project_id).await;
    match project {
        Ok(file_url) => {
            let file_name = file_url;
            let client = &state.tenant_minio_client;
            let result = download_file_as_bytes(&client, &state.env.minio_bucket.clone(), &file_name).await;

            match result {
                Ok(bytes) => {
                    let mut headers = HeaderMap::new();
                    headers.insert(
                        header::CONTENT_TYPE,
                        HeaderValue::from_static("text/csv"),
                    );
                    let downloded_file_name = "golden_source.csv";
                    headers.insert(
                        header::CONTENT_DISPOSITION,
                        HeaderValue::from_str(&format!("attachment; filename=\"{}\"", &downloded_file_name)).unwrap(),
                    );
                    Ok((StatusCode::OK, headers, bytes).into_response())
                },
                Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
            }
        }
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}

pub async fn download_file_as_bytes(client: &Client, bucket: &str, key: &str) -> Result<Bytes, anyhow::Error> {
    let response = client.get_object(bucket, key).send().await?;
    let buffer = response.content.to_segmented_bytes().await?;
    let bytes = buffer.to_bytes();
    Ok(bytes)
}
