pub mod golden_source;
pub mod alpha_snapshot;

use axum::{
    Router,
    extract::DefaultBodyLimit,
    routing::{get, post},
};
use crate::models::AppState;
use std::sync::Arc;

pub fn router() -> Router<Arc<AppState>> {
    Router::new()
        .route("/download-golden-source-csv/{project_id}", get(golden_source::download_golden_source_csv))
        .route("/get-alpha-snapshot/{run_id}", get(alpha_snapshot::get_alpha_snapshot))
}