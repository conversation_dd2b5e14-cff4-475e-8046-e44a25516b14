use alpha_core_db::tenant_clickhouse::reconciliation::project::{ProjectResponse, ReconProject};
use chrono::{DateTime, NaiveDate, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ReconProjectRsp {
    pub id: String,
    pub name: String,
    pub description: String,
    pub user_name: String,
    pub num_of_runs: i32,
    pub golden_source_file: String,
    #[schema(value_type = String, example = "2022-01-22")]
    pub off_set_date: NaiveDate,
    #[schema(value_type = String, example = "2022-01-22")]
    pub created_at: DateTime<Utc>,
    #[schema(value_type = String, example = "2022-01-22")]
    pub updated_at: DateTime<Utc>,
    #[schema(value_type = Option<String>, example = "2022-01-22")]
    pub ended_at: Option<DateTime<Utc>>,
    pub is_active: bool,
}

impl From<ReconProject> for ReconProjectRsp {
    fn from(project: ReconProject) -> Self {
        ReconProjectRsp {
            id: project.id,
            name: project.name,
            description: project.description,
            user_name: project.user_name,
            num_of_runs: project.num_of_runs,
            off_set_date: project.off_set_date,
            golden_source_file: project.golden_source_file,
            created_at: project.created_at,
            ended_at: project.ended_at,
            updated_at: project.updated_at,
            is_active: project.is_active,
        }
    }
}

impl From<ProjectResponse> for ReconProjectRsp {
    fn from(project: ProjectResponse) -> Self {
        ReconProjectRsp {
            id: project.id,
            name: project.name,
            description: project.description,
            user_name: project.user_name,
            num_of_runs: project.num_of_runs,
            off_set_date: project.off_set_date,
            golden_source_file: project.golden_source_file,
            created_at: project.created_at,
            ended_at: project.ended_at,
            updated_at: project.updated_at,
            is_active: project.is_active,
        }
    }
}
