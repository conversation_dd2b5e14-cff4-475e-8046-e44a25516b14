use std::sync::Arc;

use crate::{log_actions::Action, types::ComputeOrderRsp};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};

use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};

use crate::{
    models::{AppState, TokenClaims},
    oms::OrderComputer,
    types::ClientComputedOrders,
};
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Deserialize)]
pub struct ComputeOrder {
    pub portfolio_id: String,
    pub capital: f64,
}

#[tracing::instrument(fields(module = "Additional-deployment", event_id = %generate_event_id()), skip_all)]
pub async fn compute_orders_for_additional_capital_deployments(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    query: Query<ComputeOrder>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Compute orders for additional capital deployment",
            Action::ComputeOrderForAdditionalCapital,
        )
        .add_metadata("portfolio_id", &query.portfolio_id)
        .add_metadata("capital", &query.capital.to_string())
        .add_metadata("created_by", &user_name),
    );

    let order = OrderComputer::new(
        state.db.clone(),
        state.master_db.clone(),
        state.tenant_redis_url.clone(),
        state.master_redis_url.clone(),
        tenant.name.clone(),
        query.capital,
    );

    let (session_id, orders) = order
        .compute_additional_deployment(query.portfolio_id.clone())
        .await
        .map_err(|e| {
            log_error(
                LogBuilder::system(format!("Failed to compute order from additional capital : {:?}", e).as_str())
                    .add_metadata("portfolio_id", &query.portfolio_id)
                    .add_metadata("created_by", &tenant.name)
                    .add_metadata("capital", &query.capital.to_string()),
            );

            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Database error:"),
            });
            (StatusCode::INTERNAL_SERVER_ERROR, Json(error_response))
        })?;

    log_info(
        LogBuilder::system("RES: Order computed successfully")
            .add_metadata("portfolio_id", &query.portfolio_id)
            .add_metadata("created_by", &user_name)
            .add_metadata("capital", &query.capital.to_string()),
    );

    Ok::<(StatusCode, Json<ComputeOrderRsp>), (StatusCode, Json<Value>)>((
        StatusCode::CREATED,
        Json(ComputeOrderRsp { session_id, orders }),
    ))
}

#[cfg(test)]
mod tests {
    use tracing_test::traced_test;

    use super::*;

    #[tokio::test]
    #[traced_test]
    async fn test_bulk_additional_deployment() {
        dotenv::dotenv().ok();
        let pool = alpha_core_db::connection::connect_to_mssql(10).await;
        let matser_pool = alpha_core_db::connection::connect_to_master_data(10).await;

        let master_redis_url = std::env::var("MASTER_REDIS_URL").expect("MASTER_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(master_redis_url);
        let master_redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let redis_url = std::env::var("TENANT_REDIS_URL").expect("TENANT_REDIS_URL NOT FOUND");
        let mut cfg = deadpool_redis::Config::from_url(redis_url);
        let redis = cfg
            .create_pool(Some(deadpool_redis::Runtime::Tokio1))
            .expect("FAILED to create Pool");

        let mut pool_conn = pool.get().await.unwrap();
        let mut master_conn = matser_pool.get().await.unwrap();
        let portfolio_id = String::from("022c20dadd004b03971b36597594bd5f");
        let capital = 434355f64;
        let created_by = String::from("Athul");

        let order = OrderComputer::new(pool, matser_pool, redis, master_redis, created_by, capital);
        let orders = order.compute_additional_deployment(portfolio_id).await.unwrap();
        println!("{:?}", orders);
    }
}
