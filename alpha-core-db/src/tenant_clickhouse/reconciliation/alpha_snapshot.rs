use chrono::NaiveDate;
use clickhouse::{Client, Row};
use serde::{Deserialize, Serialize};

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct AlphaHolding {
    pub holding_date: NaiveDate,
    pub client_code: String,
    pub name: String,
    pub asset_class: String,
    pub symbol: String,
    pub security_name: String,
    pub isin: String,
    pub holdings: f64,
    pub unit_cost: f64,
    pub total_cost: f64,
    pub unit_price: f64,
    pub accrued_income: f64,
    pub market_value: f64,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct AlphaSnapshot {
    #[serde(rename = "RunId")]
    pub run_id: String,

    #[serde(rename = "HoldingDate", with = "clickhouse::serde::chrono::date")]
    pub holding_date: NaiveDate,

    #[serde(rename = "ClientCode")]
    pub client_code: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Symbol")]
    pub symbol: String,

    #[serde(rename = "SecurityName")]
    pub security_name: String,

    #[serde(rename = "Isin")]
    pub isin: String,

    #[serde(rename = "Holdings")]
    pub holdings: f64,

    #[serde(rename = "UnitCost")]
    pub unit_cost: f64,

    #[serde(rename = "TotalCost")]
    pub total_cost: f64,

    #[serde(rename = "AccruedIncome")]
    pub accrued_income: f64,

    #[serde(rename = "MarketValue")]
    pub market_value: f64,
}

pub struct AlphaSnapshotFilter {
    pub security_name: Option<String>,
    pub isin: Option<String>,
    pub symbol: Option<String>,
    pub client_code: Option<String>,
}

impl AlphaSnapshot {
    pub async fn insert(conn: &Client, row: AlphaSnapshot) -> Result<(), clickhouse::error::Error> {
        let mut insert = conn.insert::<Self>("AlphaSnapshot")?;
        insert.write(&row).await?;
        insert.end().await?;
        Ok(())
    }

    pub async fn insert_batch(conn: &Client, rows: Vec<AlphaSnapshot>) -> Result<(), clickhouse::error::Error> {
        let mut insert = conn.insert::<Self>("AlphaSnapshot")?;
        for row in rows {
            insert.write(&row).await?;
        }
        insert.end().await?;
        Ok(())
    }

    // This is in tenant_clickhouse_client
    pub async fn snapshot_holdings(
        conn: &Client,
        holdings: Vec<AlphaHolding>,
        run_id: &str,
    ) -> Result<Vec<AlphaSnapshot>, clickhouse::error::Error> {
        let alpha_snapshots: Vec<AlphaSnapshot> = holdings
            .into_iter()
            .map(|holding| AlphaSnapshot {
                run_id: run_id.to_string(),
                holding_date: holding.holding_date,
                client_code: holding.client_code,
                name: holding.name,
                symbol: holding.symbol,
                security_name: holding.security_name,
                isin: holding.isin,
                holdings: holding.holdings,
                unit_cost: holding.unit_cost,
                total_cost: holding.total_cost,
                accrued_income: holding.accrued_income,
                market_value: holding.market_value,
            })
            .collect();
        
        for (chunk_index, chunk) in alpha_snapshots.chunks(1000).enumerate() {
            println!("Inserting chunk {} ({} alpha snapshots)", chunk_index + 1, chunk.len());

            let mut inserter = conn.inserter::<AlphaSnapshot>("AlphaSnapshot")?;

            for (i, row) in chunk.iter().enumerate() {
                inserter.write(row)?;
            }

            inserter.end().await?;
        }
        println!("✅ Inserted {} alpha snapshots", alpha_snapshots.len());
        Ok(alpha_snapshots)
    }

    pub async fn get_filtered(
        conn: &Client,
        run_id: &str,
        filter: AlphaSnapshotFilter,
        limit: u64,
        offset: u64,
    ) -> Result<(u64, Vec<AlphaSnapshot>), clickhouse::error::Error> {
        let mut where_sql = String::from("RunId = ?");
        let mut bind_values = vec![run_id.to_string()];

        if let Some(ref name) = filter.security_name {
            where_sql.push_str(" AND lower(SecurityName) LIKE ?");
            bind_values.push(format!("%{}%", name.to_lowercase()));
        }

        if let Some(ref isin) = filter.isin {
            where_sql.push_str(" AND lower(Isin) LIKE ?");
            bind_values.push(format!("%{}%", isin.to_lowercase()));
        }

        if let Some(ref symbol) = filter.symbol {
            where_sql.push_str(" AND lower(Symbol) LIKE ?");
            bind_values.push(format!("%{}%", symbol.to_lowercase()));
        }

        if let Some(ref client_code) = filter.client_code {
            where_sql.push_str(" AND lower(ClientCode) LIKE ?");
            bind_values.push(format!("%{}%", client_code.to_lowercase()));
        }

        // Count query
        let mut count_query = conn.query(&format!("SELECT count() FROM AlphaSnapshot WHERE {}", where_sql));
        for value in &bind_values {
            count_query = count_query.bind(value);
        }
        let count_result = count_query.fetch_one().await?;

        // Data query
        let mut data_query = conn.query(&format!(
            "SELECT * FROM AlphaSnapshot WHERE {} LIMIT ? OFFSET ?",
            where_sql
        ));
        for value in &bind_values {
            data_query = data_query.bind(value);
        }
        data_query = data_query.bind(limit).bind(offset);
        let data_result = data_query.fetch_all::<AlphaSnapshot>().await?;

        Ok((count_result, data_result))
    }
}



