use alpha_core_db::tenant_clickhouse::reconciliation::recon_run::ReconProjectRuns;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use utoipa::ToSchema;

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ReconProjectRunsRsp {
    pub id: String,
    pub project_id: String,
    pub serial_no: i32,
    pub matched_row_count: i32,
    pub unmatched_row_count: i32,
    pub partially_matched_row_count: i32,
    pub not_found_in_client_row_count: i32,
    pub not_found_in_alpha_row_count: i32,
    #[schema(value_type = String, example = "2022-01-22")]
    pub run_date: DateTime<Utc>,
}

impl From<ReconProjectRuns> for ReconProjectRunsRsp {
    fn from(run: ReconProjectRuns) -> Self {
        ReconProjectRunsRsp {
            id: run.id,
            project_id: run.project_id,
            serial_no: run.serial_no,
            matched_row_count: run.matched_row_count,
            unmatched_row_count: run.unmatched_row_count,
            partially_matched_row_count: run.partially_matched_row_count,
            not_found_in_client_row_count: run.not_found_in_client_row_count,
            not_found_in_alpha_row_count: run.not_found_in_alpha_row_count,
            run_date: Utc::now(), // This should be assigned from database
        }
    }
}

#[derive(Serialize, ToSchema)]
#[serde(rename_all = "camelCase")]
pub struct ReconProjectRunDetailsRsp {
    pub id: String,
    pub project_id: String,
    pub serial_no: i32,
    pub matched_row_count: i32,
    pub unmatched_row_count: i32,
    pub partially_matched_row_count: i32,
    pub not_found_in_client_row_count: i32,
    pub not_found_in_alpha_row_count: i32,
    #[schema(value_type = String, example = "2022-01-22")]
    pub run_date: DateTime<Utc>,
}

impl From<ReconProjectRuns> for ReconProjectRunDetailsRsp {
    fn from(run: ReconProjectRuns) -> Self {
        ReconProjectRunDetailsRsp {
            id: run.id,
            project_id: run.project_id,
            serial_no: run.serial_no,
            matched_row_count: run.matched_row_count,
            unmatched_row_count: run.unmatched_row_count,
            partially_matched_row_count: run.partially_matched_row_count,
            not_found_in_client_row_count: run.not_found_in_client_row_count,
            not_found_in_alpha_row_count: run.not_found_in_alpha_row_count,
            run_date: Utc::now(), // This should be assigned from database
        }
    }
}
