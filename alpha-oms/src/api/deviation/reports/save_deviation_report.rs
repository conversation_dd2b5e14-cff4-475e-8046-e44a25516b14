use crate::{
    log_actions::Action,
    oms::deviation::{DeviationSession, ReportsToBeUpdated},
};
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info, log_warn};
use alpha_core_db::schema::oms::PortfolioDetailsForOms;
use alpha_utils::constants::RedisKey;
use axum::{
    extract::{Query, State},
    http::StatusCode,
    response::IntoResponse,
    Extension, Json,
};
use serde::{Deserialize, Serialize};
use serde_json::json;
use std::{collections::HashSet, sync::Arc};

use crate::{
    models::{AppState, TokenClaims},
    types::deviation::ModelDeviationReport,
};

use super::update_reports_in_bulk_and_add_portfolio_details;

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ReportToChange {
    report_id: u64,
    select: bool,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveDeviationReportsQuery {
    id: String,
    changes: Vec<ReportToChange>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct SaveDeviationReportsResponse {
    pub status: String,
}

/// This save the Deviation Report
/// Report id to save should be included in the Json Body
#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn save_deviation_reports(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(query): Json<SaveDeviationReportsQuery>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Save Deviation report",
            Action::SaveDeviationReports,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &query.id),
    );

    let deviation_session = DeviationSession::build(query.id.clone(), state.clone());
    let reports_to_update = query
        .changes
        .iter()
        .map(|change| ReportsToBeUpdated {
            report_id: change.report_id,
            selected: change.select,
        })
        .collect();

    match deviation_session.update_reports(reports_to_update).await {
        Ok(_) => {}
        Err(err) => {
            let error_response = json!({
                "status": "error",
                "message": "Failed to update reports in Redis",
            });
            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    }

    log_info(
        LogBuilder::system("RES: Successfully updated deviation reports and portfolio details")
            .add_metadata("deviation_id", &query.id)
            .add_metadata("user_id", &user_id)
            .add_metadata("user_name", &user_name),
    );

    Ok::<(StatusCode, axum::Json<SaveDeviationReportsResponse>), (StatusCode, axum::Json<serde_json::Value>)>((
        StatusCode::ACCEPTED,
        Json(SaveDeviationReportsResponse {
            status: "success".to_owned(),
        }),
    ))
}
