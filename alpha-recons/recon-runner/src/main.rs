use alpha_core_db::minio::download_file;
use alpha_utils::rabbit_mq::RabbitMq;
use futures::lock::Mutex;
use futures_lite::StreamExt;
use lapin::options::BasicAckOptions;
use recon_util::{client_report::process::process_client_report, message_type::MessageContent, recon_engine};
use serde_json::Value;

#[tokio::main]
async fn main() {
    dotenv::dotenv().ok();

    let queue_name = std::env::var("RECON_QUEUE_NAME").expect("RECON_QUEUE_NAME NOT FOUND");
    let bucket_name = std::env::var("MINIO_BUCKET").expect("MINIO_BUCKET NOT FOUND");

    let alpha_db = alpha_core_db::connection::connect_to_mssql(2).await;
    let master_client = alpha_core_db::clickhouse::create_clickhouse_client();
    let tenant_clickhouse = alpha_core_db::tenant_clickhouse::connect_tenant_recon_clickhouse_client();
    let minio_client = alpha_core_db::minio::connect_to_minio()
        .await
        .expect("Error connecting to minio");
    let connection = RabbitMq::connect_rabbit_mq().await;

    // let mut queue_consumer = connection.create_consumer(&format!("{queue_name}/recon-runner")).await;
    let mut queue_consumer = connection.create_consumer(&queue_name).await;

    while let Some(delivery) = queue_consumer.next().await {
        let alpha_db = alpha_db.clone();
        let minio_client = minio_client.clone();
        let bucket_name = bucket_name.clone();
        let tenant_clickhouse = tenant_clickhouse.clone();
        let master_client = master_client.clone();

        tokio::spawn(async move {
            let delivery = delivery.expect("Failed to process queue delivery msg");
            let value: Value = match serde_json::from_slice(&delivery.data) {
                Ok(value) => value,
                Err(err) => {
                    return;
                }
            };
            let message: MessageContent = match serde_json::from_value(value) {
                Ok(msg) => {
                    println!("Received message: {:?}", msg);
                    msg
                }
                Err(err) => {
                    println!("Error parsing message: {:?}", err);
                    return;
                }
            };

            match message {
                MessageContent::ProcessClientHoldings(message) => {
                    println!("Processing client holdings");
                    // acknowledge the msg
                    if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                        println!("Error acknowledging message: {:?}", err);
                    };

                    let file_name = message.file_name;
                    let project_id = message.project_id;

                    tokio::spawn(async move {
                        // download the file from minio
                        let file_path = download_file(&minio_client, &bucket_name, &file_name)
                            .await
                            .expect("Error downloading file");

                        println!("File downloaded: {:?}", file_path);
                        // read the
                        process_client_report(&file_path, &project_id, &tenant_clickhouse)
                            .await
                            .expect("Error processing file");
                    });
                }
                MessageContent::RunRecon(message) => {
                    println!("Running recon for project: {:?}", message.project_id);
                    tokio::spawn(async move {
                        if let Err(err) = delivery.ack(BasicAckOptions::default()).await {
                            println!("Error acknowledging message: {:?}", err);
                        };

                        recon_engine::run(&alpha_db, &master_client, &tenant_clickhouse, message.project_id).await;
                    });
                }
            }
        });
    }
}
