use std::{collections::HashMap, sync::Arc};

use alpha_core_db::{
    connection::pool::{deadpool::managed::Pool, Manager},
    schema::capital_register::PortfolioCapitalRegister,
    types::storage_engine::StorageEngineTransactions,
};
use alpha_utils::types::{SecurityDetails, SecurityTypeForPrice};
use chrono::NaiveDate;
use rust_decimal::{prelude::FromPrimitive, Decimal};
use rust_decimal_macros::dec;
use tracing::error;

use crate::{
    chrono_to_time_date,
    states::{
        analytics::TransactionForAnalytics,
        aum::Aum,
        capital_register::CapitalRegister,
        enums::{SecurityType, TransactionSubType, TransactionType},
        investment::Investment,
        ledger::{Ledger, LedgerTransaction},
        legacy_ledger::LegacyLedger,
        metrics::{
            benchmark::BenchmarkXirr,
            portfolio::{TwrrResults, XirrResults},
        },
        peak_margin::PeakMargin,
        portfolio::Portfolio,
        quantity_queue::QuantityQueue,
        receivable_payable::ReceivablePayable,
        transaction::Transaction,
    },
    storage::{Storage, StorageWriter},
    types::EligbleStateTransitionTransactions,
    utils::{
        benchmark::BenchmarkIndices,
        financial::{
            twrr::TwrrCalculator,
            xirr::{InvestmentXirr, PortfolioXirrCalculator},
        },
        transaction_computer::TransactionComputer,
    },
};

use super::block_state::BlockState;

/// First block of a portfolio
pub struct GenesisBlock {
    /// Main Database connection pool
    pub db_pool: Pool<Manager>,

    /// A pool of master_data db connection (TODO: This should be removed)
    pub master_db_pool: Pool<Manager>,

    /// A pool of redis Connections
    pub redis_pool: deadpool::managed::Pool<deadpool_redis::Manager, deadpool_redis::Connection>,

    /// A Pool Of Clickhouse connections
    pub clickhouse_pool: clickhouse::Client,

    /// Client Id of the state
    pub client_id: String,

    /// Portfolio Id of the state
    pub portfolio_id: String,

    ///Model Of of the state
    pub strategy_id: String,

    ///Model If of the state
    pub model_id: String,

    /// State For the Portfolio to be computed
    pub date: NaiveDate,

    /// State Storage
    pub storage_db: Arc<Storage>,

    /// Transactions will
    pub transactions: Vec<Transaction>,
}

impl GenesisBlock {
    pub async fn compute_genesis(&mut self) -> anyhow::Result<()> {
        let txn = self
            .get_transactions()
            .await
            .map_err(|_e| anyhow::anyhow!("No CR for the POrtfolio"))?;

        self.transactions = txn.investment_transactions;

        let mut storage = StorageWriter::new(self.storage_db.db.clone());

        storage.begin_batch();

        let date = self.date;

        //Group the transaction by  isin
        let transactions_by_isin = self
            .transactions
            .clone()
            .into_iter()
            .fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.isin.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        let mut new_investment_state = Vec::new();
        let mut redis_conn = self.redis_pool.get().await.unwrap();

        let mut transaction_analytics = Vec::new();

        // Build the investment from the transaction
        for (isin, transactions) in transactions_by_isin {
            let mut invested_capital = dec!(0);
            let mut quantity_queue = QuantityQueue::new();

            let eligible_txn: Vec<&Transaction> = transactions
                .iter()
                .filter(|f| f.transaction_type == TransactionType::Buy || f.transaction_type == TransactionType::Sell)
                .collect();

            let single_transaction = transactions[0].clone();

            for tx in &transactions {
                //If the transaction is Sell Reduce the quantity from quantity queue

                if tx.transaction_type == TransactionType::Sell {
                    let capital = quantity_queue.reduce_quantity(tx.quantity).unwrap();
                    invested_capital -= capital;
                } else if tx.transaction_type == TransactionType::Buy {
                    quantity_queue.add_quantity(tx.quantity, tx.price, tx.transaction_date.into());
                    invested_capital += tx.amount;
                }
            }

            //Store the quantity_queue
            storage
                .insert_quantity_queue(self.portfolio_id.clone(), isin.clone(), self.date, quantity_queue)
                .await;

            let click_date = chrono_to_time_date(self.date);

            let mut security_details = SecurityDetails::build_as_at(
                self.clickhouse_pool.clone(),
                &mut redis_conn,
                self.master_db_pool.clone(),
                isin.clone(),
                click_date,
                SecurityTypeForPrice::Equity,
            )
            .await
            .expect(&format!("Security Details and Price, Isin =  {}", isin));

            let mut price = storage.get_nse_price(&isin, self.date).unwrap_or(0f64);

            if price == 0f64 {
                price = storage
                    .get_nse_price(&single_transaction.symbol, self.date)
                    .unwrap_or(0f64);
                if price == 0f64 {
                    //Check if the isin change
                    let old_isin = storage.get_old_isin(&isin);
                    if let Some(isin) = old_isin {
                        price = storage.get_nse_price(&isin, self.date).unwrap_or(0f64);
                    } else {
                        price = storage.get_bse_price(&isin, self.date).unwrap_or(0f64);

                        if price == 0f64 {
                            price = storage.get_mf_price(&isin, self.date).unwrap_or(0f64);
                        }
                    }
                }
            }
            security_details.set_price(price);

            let txn_computer = TransactionComputer;
            let (total_capital, total_withdrawals, dividends_paid) =
                txn_computer.calculate_capital_value(&eligible_txn);

            let new_holdings = txn_computer.calculate_change_in_holdings_from_transactions(&eligible_txn);

            let unit_holding_split =
                txn_computer.long_short_term_holdings(&security_details.get_company_name(), &eligible_txn);

            let new_invested_capital = invested_capital;

            let price_to_consider = if Decimal::from_f64(security_details.get_latest_price()).unwrap() > dec!(0) {
                Decimal::from_f64(security_details.get_latest_price()).unwrap()
            } else {
                single_transaction.price.round_dp(2)
            };

            let new_market_value = (new_holdings * price_to_consider).round_dp(2);

            let new_average_price = if new_holdings > dec!(0) {
                (new_invested_capital / new_holdings).round_dp(2)
            } else {
                dec!(0)
            };

            let new_absolute_return_value = (new_market_value + dividends_paid) - new_invested_capital;
            let absolute_return_percent = if new_invested_capital == dec!(0) {
                dec!(0)
            } else {
                (new_absolute_return_value / new_invested_capital).round_dp(2)
            };

            let mut investment_genesis = Investment {
                portfolio_id: self.portfolio_id.clone(),
                absolute_return_percent,
                absolute_return_value: new_absolute_return_value,
                asset_class: security_details.get_asset_class(),
                asset_type: security_details.get_company_name().clone(),
                market_value: new_market_value,
                close_price: Decimal::from_f64(security_details.get_latest_price()).unwrap(),
                close_price_change: Decimal::from_f64(security_details.get_latest_price()).unwrap(),
                close_price_date: self.date.into(),
                current_price: price_to_consider,
                date: self.date.into(),
                dividends_paid,
                exchange: single_transaction.exchange.clone(),
                holdings: new_holdings,
                industry: security_details
                    .get_industry()
                    .clone()
                    .unwrap_or("Not Available".to_string()),
                invested_capital,
                isin: security_details.get_isin().clone(),
                withdrawals: total_withdrawals,
                total_capital,
                symbol: security_details.get_identifier(),
                name: security_details.get_company_name(),
                market_value_change: dec!(0),
                market_value_change_percent: dec!(0),
                long_term_units: unit_holding_split.0,
                short_term_units: unit_holding_split.1,
                client_id: self.client_id.clone(),
                market_cap: security_details.get_marketcap(),
                realised_gain_loss: dec!(0),
                unrealised_gain_loss: invested_capital,
                series: security_details.get_series(),
                xirr: dec!(0),
                irr_current: dec!(0),
                irr_inception: dec!(0),
                average_price: new_average_price,
                security_type: SecurityType::Stocks,
                weight: dec!(0),
            };

            investment_genesis.calculate_unrealised_gain_loss();
            investment_genesis.calculate_realised_gain_loss(&eligible_txn);

            let mut transactions_for_analytics: Vec<TransactionForAnalytics> = transactions
                .iter()
                .map(|f| {
                    return TransactionForAnalytics {
                        amount: if f.transaction_type == TransactionType::Buy {
                            f.amount
                        } else {
                            -f.amount
                        },
                        asset_class: investment_genesis.asset_class.clone(),
                        asset_type: investment_genesis.asset_type.clone(),
                        isin: investment_genesis.isin.clone(),
                        market_cap: investment_genesis.market_cap.clone(),
                        industry: investment_genesis.industry.clone(),
                        transaction_date: f.transaction_date.date(),
                    };
                })
                .collect();

            transaction_analytics.append(&mut transactions_for_analytics);

            new_investment_state.push(investment_genesis);
        }

        //Build the Portfolio
        let mut portfolio_genesis = self.build_portfolio_from_capital_register(&txn.capital_register_transactions);

        portfolio_genesis.portfolio_id = self.portfolio_id.clone();
        portfolio_genesis.date = self.date;

        //2.a Apply the new investment computed to the portfolio (Updates the Market Value)
        portfolio_genesis.apply_investments(&new_investment_state);

        //3. Process Cash Ledgers Transactions on top of the Portfolio State (Updated the Cash Of the Portfolio)
        // Create a vector of references to LedgerTransaction trait objects
        let merged_transactions: Vec<&dyn LedgerTransaction> = txn
            .legacy_ledger_transactions
            .iter()
            .map(|it| it as &dyn LedgerTransaction)
            .collect();

        let (ledgers, current_balance, current_assets_and_liabilities) =
            compute_ledger_entries_from_transactions(merged_transactions);

        portfolio_genesis.update_balance(current_balance);
        portfolio_genesis.assets_and_liabilities = current_assets_and_liabilities;

        let (receivables, payables) =
            portfolio_genesis.process_receivables_payables(&txn.receivables_payable_transactions);

        let portfolio_id = self.portfolio_id.clone();

        let cash_flow: Decimal = txn
            .capital_register_transactions
            .iter()
            .map(|c| {
                if c.transaction_type == TransactionType::Inflow {
                    return c.amount;
                } else if c.transaction_type == TransactionType::Outflow {
                    return -c.amount;
                }
                dec!(0)
            })
            .sum();

        //Insert Payables
        for (date, amount) in payables {
            //Check if there is already any payable on that date
            let existing_payables = storage.get_payables(&portfolio_id, date);

            //Insert the new payables
            storage
                .insert_payables(portfolio_id.clone(), date, amount + existing_payables)
                .await;
        }

        //Insert Receivables
        for (date, amount) in receivables {
            //Check if there is already any payable on that date
            let existing_receivables = storage.get_receivables(&portfolio_id, date);

            //Insert the new payables
            storage
                .insert_receivables(portfolio_id.clone(), date, amount + existing_receivables)
                .await;
        }

        //Get the Payables that needs to subtract to cash on date T
        let payables_for_today = storage.get_payables(&portfolio_genesis.portfolio_id, self.date);

        //Get the Receivables that needs to add to cash on date T
        let receivables_for_today = storage.get_receivables(&portfolio_genesis.portfolio_id, self.date);

        //Apply Payables and Receivables On Cash
        // portfolio_genesis.balance -= payables_for_today;
        // portfolio_genesis.balance += receivables_for_today;

        portfolio_genesis.payables -= payables_for_today;
        portfolio_genesis.receivables -= receivables_for_today;

        let aum = Aum::build_genesis(
            portfolio_genesis.balance,
            portfolio_genesis.market_value,
            cash_flow,
            date,
            portfolio_genesis.payables,
            portfolio_genesis.receivables,
        );

        //3 .Update Unrealised gain loss
        portfolio_genesis.update_unrealised_gain_loss();

        //Commit Portfolio Start Date
        storage
            .insert_portfolio_start_date(self.date, &portfolio_genesis.portfolio_id)
            .await;

        //Commit This Portfolio client
        storage
            .insert_clients_portfolio(&self.client_id, portfolio_id.clone(), self.date)
            .await;
        storage.insert_client_start_date(&self.client_id, self.date).await;

        //Commit Strategy
        storage.insert_strategy_portfolio(&self.strategy_id, portfolio_id.clone(), self.date);
        storage.insert_strategy_start_date(&self.strategy_id, self.date);

        //Commit the Strategy Model
        storage
            .insert_strategy_models_portfolio(&self.model_id, portfolio_id.clone(), self.date)
            .await;
        storage
            .insert_strategy_model_start_date(&self.model_id, self.date)
            .await;

        let state_result = BlockState {
            aum: aum.clone(),
            capital_registers: txn.capital_register_transactions,
            date,
            investment_transactions: self.transactions.clone(),
            investments: new_investment_state.clone(),
            ledgers,
            portfolio_state: portfolio_genesis,
            transaction_for_analytics: transaction_analytics.clone(),
        };

        //We need to commit all changes before calculating analytics
        state_result.commit(storage).await;

        let mut storage = StorageWriter::new(self.storage_db.db.clone());

        self.compute_investments_analytics(
            &mut storage,
            &portfolio_id,
            &transaction_analytics,
            new_investment_state,
        )
        .await;

        self.compute_portfolio_analytics(&mut storage, &portfolio_id, aum).await;
        let _ = storage.commit_batch().await;
        Ok(())
    }

    pub async fn get_transactions(&mut self) -> Result<EligbleStateTransitionTransactions, String> {
        let mut conn = self.db_pool.get().await.unwrap();
        let capital_registers = PortfolioCapitalRegister::get_the_lowest_date_entries(&mut conn, &self.portfolio_id)
            .await
            .unwrap();

        let capital_register_transactions: Vec<CapitalRegister> =
            capital_registers.iter().map(CapitalRegister::from_row).collect();

        if capital_register_transactions.len() == 0 {
            error!("Portfolio Doesn't Have CR");
            return Err(String::from("Portfolio Doesn't Have CR"));
        }

        let portfolio_start_date = capital_register_transactions[0].transaction_date;

        let transactions =
            StorageEngineTransactions::get_txns_for_genesis(&mut conn, &self.portfolio_id, portfolio_start_date.into())
                .await;

        let capital_register_transactions: Vec<CapitalRegister> =
            serde_json::from_str(&transactions.capital_registers).unwrap();

        let investment_transactions: Vec<Transaction> =
            serde_json::from_str(&transactions.investment_transactions).unwrap();

        let peak_margin_transactions: Vec<PeakMargin> = serde_json::from_str(&transactions.peak_margin_logs).unwrap();

        let legacy_ledger_transactions: Vec<LegacyLedger> = serde_json::from_str(&transactions.cash_ledgers).unwrap();

        let receivables_payable_transactions: Vec<ReceivablePayable> =
            serde_json::from_str(&transactions.receivable_payables).unwrap();

        self.date = portfolio_start_date.into();

        Ok(EligbleStateTransitionTransactions {
            capital_register_transactions,
            investment_transactions,
            peak_margin_transactions,
            legacy_ledger_transactions,
            receivables_payable_transactions,
        })
    }

    /// Computes the value for Portfolio from Capital Register
    fn compute_capital_values(&self, capital_register_txns: &Vec<CapitalRegister>) -> (Decimal, Decimal, Decimal) {
        let total_capital: Decimal = capital_register_txns
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Inflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalIn
                        || txn.transaction_sub_type == TransactionSubType::SecurityIn)
            })
            .map(|txn| txn.amount)
            .sum();

        let total_withdrawal: Decimal = capital_register_txns
            .iter()
            .filter(|txn| {
                txn.transaction_type == TransactionType::Outflow
                    && (txn.transaction_sub_type == TransactionSubType::CapitalOut
                        || txn.transaction_sub_type == TransactionSubType::SecurityOut)
            })
            .map(|txn| txn.amount)
            .sum();

        let invested_capital: Decimal = total_capital - total_withdrawal;

        (total_capital, total_withdrawal, invested_capital)
    }

    /// Compute the Analytics for the Given portfolio at Date T
    async fn compute_portfolio_analytics(&self, storage: &mut StorageWriter, portfolio_id: &str, current_aum: Aum) {
        let next_date = self.date;

        let aums = storage.get_aum_for_analytics_range(next_date, portfolio_id);
        let aums: Vec<Aum> = aums.iter().cloned().filter(|a| a.net_cash_flows != dec!(0)).collect();

        let xirr_calculator = PortfolioXirrCalculator {
            aums,
            current_aum: current_aum.clone(),
            date: next_date,
            portfolio_id: portfolio_id.to_string(),
        };

        let calculated_xirr = xirr_calculator.calculate_all_periods(storage).unwrap();

        let xirr_state = XirrResults {
            fytd: calculated_xirr.fytd,
            one_month: calculated_xirr.one_month,
            one_year: calculated_xirr.one_year,
            seven_year: calculated_xirr.seven_year,
            since_inception: calculated_xirr.since_inception,
            six_month: calculated_xirr.six_month,
            ten_year: calculated_xirr.ten_year,
            three_month: calculated_xirr.three_month,
            three_year: calculated_xirr.three_year,
            two_year: calculated_xirr.two_year,
            ytd: calculated_xirr.ytd,
        };

        storage.insert_xirr_metrics(&portfolio_id, next_date, xirr_state).await;
        let twrr_calculator = TwrrCalculator {
            date: next_date,
            portfolio_id: portfolio_id.to_string(),
        };

        let calculated_twrr = twrr_calculator.calculate_twrr_all_periods(storage).unwrap();

        let twrr_state = TwrrResults {
            fytd: calculated_twrr.fytd.map(|f| f.into()),
            one_month: calculated_twrr.one_month,
            one_year: calculated_twrr.one_year,
            seven_year: calculated_twrr.seven_year,
            since_inception: calculated_twrr.since_inception,
            six_month: calculated_twrr.six_month,
            ten_year: calculated_twrr.ten_year,
            three_month: calculated_twrr.three_month,
            three_year: calculated_twrr.three_year,
            two_year: calculated_twrr.two_year,
            ytd: calculated_twrr.ytd,
        };

        storage
            .insert_portfolio_twrr_metrics(&portfolio_id, next_date, twrr_state)
            .await;

        //Benchmark
        // for bench in BenchmarkIndices::all() {
        //     let calculated_data = bench
        //         .calculate_xirr(portfolio_id.to_owned(), next_date, current_aum.clone(), storage)
        //         .unwrap();

        //     let data = BenchmarkXirr {
        //         fytd: calculated_data.fytd,
        //         one_month: calculated_data.one_month,
        //         one_year: calculated_data.one_year,
        //         seven_year: calculated_data.seven_year,
        //         since_inception: calculated_data.since_inception,
        //         six_month: calculated_data.six_month,
        //         ten_year: calculated_data.ten_year,
        //         three_month: calculated_data.three_month,
        //         three_year: calculated_data.three_year,
        //         two_year: calculated_data.two_year,
        //         ytd: calculated_data.ytd,
        //     };

        //     storage
        //         .insert_benchmark_xirr_metrics(portfolio_id, next_date, bench, data)
        //         .await;
        // }
    }

    /// Compute the XIRR,Trr at Investment Level based on different categories
    async fn compute_investments_analytics(
        &self,
        storage: &mut StorageWriter,
        portfolio_id: &str,
        transactions_for_analytics: &Vec<TransactionForAnalytics>,
        mut new_investment_state: Vec<Investment>,
    ) {
        let investment_xirr = InvestmentXirr { date: self.date };

        //We should first do for Holdings(Isin level) and update the investments
        let transactions_for_analytics_by_isin =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.isin.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        //Update the Investments After Calculating XIRR
        for (isin, transactions) in transactions_for_analytics_by_isin {
            let matching_investments: &mut Investment =
                new_investment_state.iter_mut().find(|inv| inv.isin == isin).unwrap(); //We are sure inv exist

            let market_value: Decimal = matching_investments.market_value;

            let xirr = investment_xirr
                .calculate_for_holdings(market_value, &transactions)
                .unwrap_or_default();

            matching_investments.xirr = Decimal::from_f64(xirr).unwrap_or_default();
        }

        storage
            .insert_portfolio_investment(&new_investment_state, portfolio_id.to_owned(), self.date)
            .await;

        //Asset Class Level Analytics computation
        let transactions_for_analytics_by_asset_class =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.asset_class.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (asset_class, transactions) in transactions_for_analytics_by_asset_class {
            let matching_investments: Vec<&Investment> = new_investment_state
                .iter()
                .filter(|inv| inv.asset_class == asset_class)
                .collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_asset_class(market_value, &transactions);
        }

        //Asset Type Level Analytics computation
        let transactions_for_analytics_by_asset_type =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.asset_type.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (asset_type, transactions) in transactions_for_analytics_by_asset_type {
            let matching_investments: Vec<&Investment> = new_investment_state
                .iter()
                .filter(|inv| inv.asset_type == asset_type)
                .collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_asset_type(market_value, &transactions);
        }

        //Market Cap Level Analytics computation
        let transactions_for_analytics_by_market_cap =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.market_cap.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (market_cap, transactions) in transactions_for_analytics_by_market_cap {
            let matching_investments: Vec<&Investment> = new_investment_state
                .iter()
                .filter(|inv| inv.market_cap == market_cap)
                .collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_market_cap(market_value, &transactions);
        }

        //Industry Level Analytics computation
        let transactions_for_analytics_by_industry =
            transactions_for_analytics.iter().fold(HashMap::new(), |mut acc, txn| {
                acc.entry(txn.industry.clone()).or_insert_with(Vec::new).push(txn);
                acc
            });

        for (industry, transactions) in transactions_for_analytics_by_industry {
            let matching_investments: Vec<&Investment> = new_investment_state
                .iter()
                .filter(|inv| inv.industry == industry)
                .collect();

            let market_value: Decimal = matching_investments.iter().map(|f| f.market_value).sum();

            let _xirr = investment_xirr.calculate_for_industry(market_value, &transactions);
        }
    }

    #[allow(unused)]
    fn calculate_current_holdings(&mut self) {
        // Sort transactions by CGT date and then by transaction date
        self.transactions.sort_by(|a, b| {
            a.cgt_date
                .cmp(&b.cgt_date)
                .then(a.transaction_date.cmp(&b.transaction_date))
        });

        let mut current_bal = dec!(0);
        for txn in self.transactions.iter_mut() {
            if txn.transaction_type == TransactionType::Buy {
                current_bal += txn.quantity;
            } else if txn.transaction_type == TransactionType::Sell {
                current_bal -= txn.quantity;
            }
            txn.current_holding = current_bal;
        }

        // Create a map of dates to indices
        let date_indices: HashMap<_, _> = self
            .transactions
            .iter()
            .enumerate()
            .map(|(i, txn)| (txn.transaction_date, i))
            .collect();

        // Process groups with more than one transaction
        let mut dates: Vec<_> = date_indices.keys().cloned().collect();
        dates.sort();

        for &date in &dates {
            let group_indices: Vec<_> = self
                .transactions
                .iter()
                .enumerate()
                .filter(|(_, txn)| txn.transaction_date == date)
                .map(|(i, _)| i)
                .collect();

            if group_indices.len() > 1 {
                let holding_prior_to_this_date = self
                    .transactions
                    .iter()
                    .filter(|txn| txn.transaction_date < date)
                    .map(|txn| txn.current_holding)
                    .last()
                    .unwrap_or(dec!(0));

                let holding_on_the_day: Decimal =
                    group_indices
                        .iter()
                        .map(|&i| &self.transactions[i])
                        .fold(dec!(0), |acc, txn| {
                            if txn.transaction_type == TransactionType::Buy {
                                acc + txn.quantity
                            } else if txn.transaction_type == TransactionType::Sell {
                                acc - txn.quantity
                            } else {
                                acc
                            }
                        });

                let final_holding = holding_prior_to_this_date + holding_on_the_day;
                for &i in &group_indices {
                    self.transactions[i].current_holding = final_holding;
                }
            }
        }
    }

    /// This creates an Instance of Portfolio State from CR
    fn build_portfolio_from_capital_register(&self, capital_register_txns: &Vec<CapitalRegister>) -> Portfolio {
        let (total_capital, total_withdrawal, invested_capital) = self.compute_capital_values(capital_register_txns);

        let portfolio_genesis = Portfolio {
            total_capital: total_capital,
            withdrawals: total_withdrawal,
            invested_capital: invested_capital,
            ..Default::default()
        };

        portfolio_genesis
    }
}

/// Generate the Ledger Entries from All Kind of Transactions
fn compute_ledger_entries_from_transactions(
    mut transactions: Vec<&dyn LedgerTransaction>,
) -> (Vec<Ledger>, Decimal, Decimal) {
    let mut ledgers = Vec::new();
    let mut current_balance = dec!(0);
    let mut _previous_balance = current_balance;

    let mut current_assets_and_liabilities = dec!(0);

    let mut _previous_running_balance = current_balance;
    let mut _previous_assets_and_liabilities = current_assets_and_liabilities;

    transactions.sort_by_key(|f| f.get_transaction_date());

    for txn in transactions {
        let txn_type = txn.get_transaction_type();
        let txn_sub_type = txn.get_transaction_sub_type();
        let txn_amount = txn.get_amount();
        let txn_date = txn.get_transaction_date();
        let settlement_date = txn.get_settlement_date();

        //FOR Legacy Ledger
        if txn_type == TransactionType::Credit {
            current_balance += txn_amount;
        } else if txn_type == TransactionType::Debit {
            current_balance -= txn_amount;
        }

        if txn_sub_type == TransactionSubType::OtherAssetsLiabilities {
            if txn_type == TransactionType::Credit {
                current_assets_and_liabilities -= txn_amount;
            } else if txn_type == TransactionType::Debit {
                current_assets_and_liabilities += txn_amount;
            }
        }

        let ledger = Ledger {
            running_balance: current_balance,
            amount: txn_amount,
            description: txn.get_description(),
            settlement_date: txn.get_settlement_date(),
            transaction_date: txn.get_transaction_date(),
            transaction_sub_type: txn.get_transaction_sub_type(),
            transaction_type: txn.get_transaction_type(),
        };

        ledgers.push(ledger);
    }

    (ledgers, current_balance, current_assets_and_liabilities)
}
