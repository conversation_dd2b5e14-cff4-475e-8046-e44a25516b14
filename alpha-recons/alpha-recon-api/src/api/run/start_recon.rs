use crate::models::AppState;
use alpha_utils::rabbit_mq::RabbitMq;
use axum::{
    Json,
    extract::{Path, State},
    http::StatusCode,
    response::IntoResponse,
};
use recon_util::message_type::*;
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Serialize, Deserialize, ToSchema)]
struct ReconRunResponse {
    message: String,
    status: String,
}

#[utoipa::path(
    post,
    tag = "Recon runs",
    path = "/recon-run/{project_id}",
    params(
        ("project_id" = String, Path, description = "Project ID")
    ),
    request_body(content = (), description = "Start Recon Payload"),
    responses(
        (status = 202, body = ReconRunResponse, description = "Recon started successfully"),
        (status = 400, body = ReconRunResponse, description = "Bad Request"),
        (status = 500, body = ReconRunResponse, description = "Internal Server Error")
    )
)]
pub async fn start_recon_api(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Path(project_id): Path<String>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    let rabbit_mq = RabbitMq::connect_rabbit_mq().await;
    let channel = rabbit_mq.create_channel(&state.env.queue_name).await.unwrap();

    // send the run-req msg to queue
    let message = MessageContent::RunRecon(RunReconRequest { project_id: project_id });

    match rabbit_mq
        .publish_message(&state.env.queue_name, serde_json::to_string(&message).unwrap())
        .await
    {
        Ok(_) => {
            println!("Message published to queue");
            return Ok::<(StatusCode, Json<ReconRunResponse>), (StatusCode, Json<ReconRunResponse>)>((
                StatusCode::ACCEPTED,
                Json(ReconRunResponse {
                    message: "Recon started successfully".to_string(),
                    status: "failed".to_string(),
                }),
            ));
        }
        Err(e) => {
            println!("Error publishing message: {}", e);
            return Err::<(StatusCode, Json<ReconRunResponse>), (StatusCode, Json<ReconRunResponse>)>((
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(ReconRunResponse {
                    message: "Error publishing message".to_string(),
                    status: "failed".to_string(),
                }),
            ));
        }
    }
}
