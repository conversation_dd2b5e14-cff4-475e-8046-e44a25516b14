FROM rust:1.85 AS chef
RUN cargo install cargo-chef
WORKDIR /app

FROM chef AS planner
COPY . .
RUN cargo chef prepare --recipe-path recipe.json

FROM chef AS builder 
COPY --from=planner /app/recipe.json recipe.json

RUN apt-get update && apt-get install -y  --no-install-recommends  \
    build-essential \
    cmake \
    clang \
    openssh-client \
    libclang-dev \
    && rm -rf /var/lib/apt/lists/*

RUN mkdir -p /root/.ssh && ssh-keyscan ssh.dev.azure.com >> /root/.ssh/known_hosts

# Build dependencies - this is the caching Docker layer!
RUN --mount=type=ssh cargo chef cook --release --recipe-path recipe.json

# Build application
COPY . .

RUN --mount=type=ssh cargo build --release --bin alpha-recon-api
RUN --mount=type=ssh cargo build --release --bin recon-runner

#Runtime
FROM gcr.io/distroless/cc-debian12 AS runtime

WORKDIR /app
COPY --from=builder /app/target/release/alpha-recon-api /usr/local/bin/alpha-recon-api
COPY --from=builder /app/target/release/recon-runner /usr/local/bin/alpha-recon-runner

EXPOSE 4010
CMD ["/usr/local/bin/alpha-recon-api"]