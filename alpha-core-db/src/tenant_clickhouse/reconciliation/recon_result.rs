use std::fmt::{write, Display};

use chrono::NaiveDate;
use clickhouse::{Client, Row};
use uuid::Uuid;

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct ReconResultBreakCategories {
    #[serde(rename = "ReconResultId")]
    pub recon_result_id: String,
    #[serde(rename = "RunId")]
    pub run_id: String,
    #[serde(rename = "ProjectId")]
    pub project_id: String,
    #[serde(rename = "Category")]
    pub category: String,
}

#[derive(Default, serde::Serialize, serde::Deserialize, Debug, Clone, Row)]
pub struct ReconResult {
    #[serde(rename = "Id")]
    pub id: String,
    #[serde(rename = "RunId")]
    pub run_id: String,
    #[serde(rename = "HoldingDate", with = "clickhouse::serde::chrono::date")]
    pub holding_date: NaiveDate,
    #[serde(rename = "ClientCode")]
    pub client_code: String,
    //FIXME: Change this to ClientName
    #[serde(rename = "Name")]
    pub name: String,
    #[serde(rename = "Symbol")]
    pub symbol: String,
    #[serde(rename = "SecurityName")]
    pub security_name: String,
    #[serde(rename = "Isin")]
    pub isin: String,
    #[serde(rename = "ClientHoldings")]
    pub client_holdings: f64,
    #[serde(rename = "AlphaHoldings")]
    pub alpha_holdings: f64,
    #[serde(rename = "HoldingDiff")]
    pub holding_diff: f64,
    #[serde(rename = "ClientUnitCost")]
    pub client_unit_cost: f64,
    #[serde(rename = "AlphaUnitCost")]
    pub alpha_unit_cost: f64,
    #[serde(rename = "UnitCostDiff")]
    pub unit_cost_diff: f64,
    #[serde(rename = "ClientTotalCost")]
    pub client_total_cost: f64,
    #[serde(rename = "AlphaTotalCost")]
    pub alpha_total_cost: f64,
    #[serde(rename = "TotalCostDiff")]
    pub total_cost_diff: f64,
    #[serde(rename = "ClientMarketValue")]
    pub client_market_value: f64,
    #[serde(rename = "AlphaMarketValue")]
    pub alpha_market_value: f64,
    #[serde(rename = "MarketValueDiff")]
    pub market_value_diff: f64,
    #[serde(rename = "ClientAccruedIncome")]
    pub client_accrued_income: f64,
    #[serde(rename = "AlphaAccruedIncome")]
    pub alpha_accrued_income: f64,
    #[serde(rename = "AccruedIncomeDiff")]
    pub accrued_income_diff: f64,
    //FIXME: Should be enum
    #[serde(rename = "MatchStatus")]
    pub match_status: String,
    #[serde(rename = "UnmatchedCount")]
    pub unmatched_count: i32,
}

#[derive(Debug, serde::Serialize, serde::Deserialize, Clone, PartialEq)]
pub enum MatchStatus {
    Matched,
    UnMatched,
    PartiallyMatched,
    NotPresentInSource,
    NotPresentInAlpha,
}

impl Display for MatchStatus {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            MatchStatus::Matched => write!(f, "Matched"),
            MatchStatus::UnMatched => write!(f, "UnMatched"),
            MatchStatus::PartiallyMatched => write!(f, "PartiallyMatched"),
            MatchStatus::NotPresentInSource => write!(f, "NotPresentInSource"),
            MatchStatus::NotPresentInAlpha => write!(f, "NotPresentInAlpha"),
        }
    }
}

pub struct ReconResultFilter {
    pub security_name: Option<String>,
    pub isin: Option<String>,
    pub symbol: Option<String>,
    pub client_code: Option<String>,
    pub client_name: Option<String>,
    pub match_status: Option<MatchStatus>,
}

impl ReconResult {
    pub async fn insert(conn: &Client, recon_results: &Vec<ReconResult>) -> Result<(), clickhouse::error::Error> {
        const CHUNK_SIZE: usize = 1000;

        for chunk in recon_results.chunks(CHUNK_SIZE) {
            println!("Inserting chunk of {} recon results", chunk.len());
            let mut inserter = conn.inserter::<ReconResult>("ReconResult")?;
            for row in chunk {
                inserter.write(row)?;
            }
            inserter.end().await?;
        }

        Ok(())
    }

    pub async fn get_by_run_id(
        conn: &Client,
        run_id: &str,
        limit: u64,
        offset: u64,
    ) -> Result<(u64, Vec<ReconResult>), clickhouse::error::Error> {
        let count_query = format!("SELECT count() FROM ReconResult WHERE RunId = '{}'", run_id);
        let count_result = conn.query(&count_query).fetch_one().await?;

        let data_query = format!(
            "SELECT * FROM ReconResult WHERE RunId = '{}' LIMIT {} OFFSET {}",
            run_id, limit, offset
        );

        let data_result = conn.query(&data_query).fetch_all::<ReconResult>().await?;

        Ok((count_result, data_result))
    }

    pub async fn get_filtered(
        conn: &Client,
        run_id: &str,
        filter: ReconResultFilter,
        limit: u64,
        offset: u64,
    ) -> Result<(u64, Vec<ReconResult>), clickhouse::error::Error> {
        let mut where_sql = String::from("RunId = ?");
        let mut bind_values = vec![run_id.to_string()];

        if let Some(ref name) = filter.security_name {
            where_sql.push_str(" AND lower(SecurityName) LIKE ?");
            bind_values.push(format!("%{}%", name.to_lowercase()));
        }

        if let Some(ref isin) = filter.isin {
            where_sql.push_str(" AND lower(Isin) LIKE ?");
            bind_values.push(format!("%{}%", isin.to_lowercase()));
        }

        if let Some(ref symbol) = filter.symbol {
            where_sql.push_str(" AND lower(Symbol) LIKE ?");
            bind_values.push(format!("%{}%", symbol.to_lowercase()));
        }

        if let Some(ref client_code) = filter.client_code {
            where_sql.push_str(" AND lower(ClientCode) LIKE ?");
            bind_values.push(format!("%{}%", client_code.to_lowercase()));
        }

        if let Some(ref client_name) = filter.client_name {
            where_sql.push_str(" AND lower(Name) LIKE ?");
            bind_values.push(format!("%{}%", client_name.to_lowercase()));
        }

        if let Some(ref match_status) = filter.match_status {
            where_sql.push_str(" AND lower(MatchStatus) = ?");
            bind_values.push(format!("{}", match_status.to_string().to_lowercase()));
        }

        // Count query
        let mut count_query = conn.query(&format!("SELECT count() FROM ReconResult WHERE {}", where_sql));
        for value in &bind_values {
            count_query = count_query.bind(value);
        }

        let count_result = count_query.fetch_one().await?;

        // Data query
        let mut data_query = conn.query(&format!(
            "SELECT * FROM ReconResult WHERE {} LIMIT ? OFFSET ?",
            where_sql
        ));
        for value in &bind_values {
            data_query = data_query.bind(value);
        }
        data_query = data_query.bind(limit).bind(offset);
        let data_result = data_query.fetch_all::<ReconResult>().await?;

        Ok((count_result, data_result))
    }
}

impl ReconResultBreakCategories {
    pub async fn insert(
        conn: &Client,
        recon_result_break_categories: &Vec<ReconResultBreakCategories>,
    ) -> Result<(), clickhouse::error::Error> {
        const CHUNK_SIZE: usize = 1000;

        for chunk in recon_result_break_categories.chunks(CHUNK_SIZE) {
            println!("Inserting chunk of {} recon result break categories", chunk.len());
            let mut inserter = conn.inserter::<ReconResultBreakCategories>("ReconResultBreakCategories")?;
            for row in chunk {
                inserter.write(row)?;
            }
            inserter.end().await?;
        }
        Ok(())
    }
}
