use alpha_core_db::tenant_clickhouse::reconciliation::alpha_snapshot::AlphaSnapshot;
use utoipa::ToSchema;

#[derive(Debug, ToSchema, Clone, serde::Serialize)]
#[serde(rename_all = "camelCase")]
pub struct AlphaSnapshotResp {
    pub holding_date: String,
    pub client_code: String,
    pub name: String,
    pub symbol: String,
    pub security_name: String,
    pub isin: String,
    pub holdings: f64,
    pub unit_cost: f64,
    pub total_cost: f64,
    pub accrued_income: f64,
    pub market_value: f64,
}

impl From<AlphaSnapshot> for AlphaSnapshotResp {
    fn from(r: AlphaSnapshot) -> Self {
        AlphaSnapshotResp {
            holding_date: r.holding_date.to_string(),
            client_code: r.client_code,
            name: r.name,
            symbol: r.symbol,
            security_name: r.security_name,
            isin: r.isin,
            holdings: r.holdings,
            unit_cost: r.unit_cost,
            total_cost: r.total_cost,
            accrued_income: r.accrued_income,
            market_value: r.market_value,
        }
    }
}
