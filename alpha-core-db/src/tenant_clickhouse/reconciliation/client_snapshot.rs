use chrono::NaiveDate;
use clickhouse::{Client, Row};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// HOLDING DATE,CODE,NAME,ASSET CLASS,SYMBOLCODE,SYMBOLNAME,ISIN,<PERSON>OSITION,Q<PERSON><PERSON>TITY,UNIT COST,TOTAL COST,UNIT PRICE,ACCRUED INCOME,MARKET VALUE

#[derive(Debug, Serialize, Deserialize)]
pub struct ClientReportRow {
    #[serde(rename = "HOLDING DATE", deserialize_with = "custom_date_format::deserialize")]
    pub holding_date: NaiveDate,
    #[serde(rename = "CODE")]
    pub client_code: String,
    #[serde(rename = "NAME")]
    pub name: String,
    #[serde(rename = "SYMB<PERSON>CODE")]
    pub symbol: String,
    #[serde(rename = "SYMBOLNAME")]
    pub security_name: String,
    #[serde(rename = "ISIN")]
    pub isin: String,
    #[serde(rename = "POSITION")]
    pub position: String,
    #[serde(rename = "QUANTITY")]
    pub holdings: f64,
    #[serde(rename = "UNIT COST")]
    pub unit_cost: f64,
    #[serde(rename = "TOTAL COST")]
    pub total_cost: f64,
    #[serde(rename = "ACCRUED INCOME")]
    pub accrued_income: f64,
    #[serde(rename = "MARKET VALUE")]
    pub market_value: f64,
}

// ClientSnapshot Table
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq, Row)]
pub struct ClientSnapshot {
    #[serde(rename = "Id")]
    pub id: String,

    #[serde(rename = "ProjectId")]
    pub project_id: String,

    #[serde(rename = "HoldingDate", with = "clickhouse::serde::chrono::date")]
    pub holding_date: NaiveDate,

    #[serde(rename = "ClientCode")]
    pub client_code: String,

    #[serde(rename = "Name")]
    pub name: String,

    #[serde(rename = "Symbol")]
    pub symbol: String,

    #[serde(rename = "SecurityName")]
    pub security_name: String,

    #[serde(rename = "Isin")]
    pub isin: String,

    #[serde(rename = "Position")]
    pub position: String,

    #[serde(rename = "Holdings")]
    pub holdings: f64,

    #[serde(rename = "UnitCost")]
    pub unit_cost: f64,

    #[serde(rename = "TotalCost")]
    pub total_cost: f64,

    #[serde(rename = "AccruedIncome")]
    pub accrued_income: f64,

    #[serde(rename = "MarketValue")]
    pub market_value: f64,
}

impl ClientSnapshot {
    pub async fn insert(
        conn: &Client,
        row: ClientReportRow,
        project_id: String,
    ) -> Result<(), clickhouse::error::Error> {
        let client_snapshot = ClientSnapshot {
            id: Uuid::new_v4().to_string(),
            project_id: project_id,
            holding_date: row.holding_date,
            client_code: row.client_code,
            name: row.name,
            symbol: row.symbol,
            security_name: row.security_name,
            isin: row.isin,
            position: row.position,
            holdings: row.holdings,
            unit_cost: row.unit_cost,
            total_cost: row.total_cost,
            accrued_income: row.accrued_income,
            market_value: row.market_value,
        };

        let mut client = conn.insert::<Self>("ClientSnapshot")?;
        client.write(&client_snapshot).await.unwrap();
        client.end().await.unwrap();
        Ok(())
    }
    pub async fn insert_batch(
        conn: &Client,
        rows: Vec<ClientReportRow>,
        project_id: String,
    ) -> Result<(), clickhouse::error::Error> {
        const CHUNK_SIZE: usize = 1000;

        let client_snapshots: Vec<ClientSnapshot> = rows
            .into_iter()
            .map(|row| ClientSnapshot {
                id: Uuid::new_v4().to_string(),
                project_id: project_id.clone(),
                holding_date: row.holding_date,
                client_code: row.client_code,
                name: row.name,
                symbol: row.symbol,
                security_name: row.security_name,
                isin: row.isin,
                position: row.position,
                holdings: row.holdings,
                unit_cost: row.unit_cost,
                total_cost: row.total_cost,
                accrued_income: row.accrued_income,
                market_value: row.market_value,
            })
            .collect();

        for chunk in client_snapshots.chunks(CHUNK_SIZE) {
            let mut inserter = conn.inserter::<ClientSnapshot>("ClientSnapshot")?;
            for row in chunk {
                inserter.write(row)?;
            }
            inserter.end().await?;
        }

        Ok(())
    }

    pub async fn get_client_snapshot_by_project_id(
        conn: &Client,
        project_id: &str,
    ) -> Result<Vec<ClientSnapshot>, clickhouse::error::Error> {
        let query = format!("SELECT * FROM ClientSnapshot WHERE ProjectId = '{}'", project_id);

        let mut stream = conn.query(&query).fetch::<ClientSnapshot>()?;
        let mut results = Vec::new();

        while let Ok(Some(row)) = stream.next().await {
            results.push(row);
        }

        Ok(results)
    }

    pub async fn prepare_client_snapshot_table(
        conn: &Client,
        project_id: &str,
    ) -> Result<(), clickhouse::error::Error> {
        let query = format!("
            ALTER TABLE ClientSnapshot 
            UPDATE ClientCode = trim(arrayElement(splitByChar('-', Name), 2)),
            Name = trim(arrayElement(splitByChar('-', Name), 1))
            WHERE ProjectId = '{}'", project_id
        );

        match conn.query(&query).execute().await {
            Ok(_) => Ok(()),
            Err(e) => {
                println!("❌ Error preparing client snapshot table: {}", e);
                Err(e)
            }
        }
    }
}
mod custom_date_format {
    use chrono::NaiveDate;
    use serde::{self, Deserialize, Deserializer};

    const FORMAT: &str = "%d-%m-%y";

    pub fn deserialize<'de, D>(deserializer: D) -> Result<NaiveDate, D::Error>
    where
        D: Deserializer<'de>,
    {
        let s = String::deserialize(deserializer)?;
        NaiveDate::parse_from_str(&s, FORMAT).map_err(serde::de::Error::custom)
    }
}
