use crate::log_actions::Action;
use crate::models::{AppState, TokenClaims};
use crate::oms::deviation::DeviationSession;
use actlogica_logs::{builder::LogBuilder, generator::generate_event_id, log_error, log_info};
use alpha_utils::constants::RedisKey;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Extension, Json};
use deadpool_redis::redis::AsyncCommands;
use serde::{Deserialize, Serialize};
use std::sync::Arc;

#[derive(Serialize, Deserialize)]
pub struct DeleteDeviationOrdersPayload {
    /// Id of the deviation
    id: String,

    /// Receives the seq for which all orders should be deleted for deviation
    seq: Vec<u64>,
}

#[tracing::instrument(fields(module = "Deviation", event_id = %generate_event_id()), skip_all)]
pub async fn delete_deviation_computed_orders(
    Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
    Json(payload): Json<DeleteDeviationOrdersPayload>,
) -> Result<impl IntoResponse, impl IntoResponse> {
    /* --------- Create tracing span for logs with module name ---------- */
    let user_name = &tenant.name;
    let user_id = &tenant.sub;

    let seq = &payload.seq.iter().map(|s| s.to_string()).collect::<Vec<_>>().join(",");
    log_info(
        LogBuilder::user(
            user_id,
            user_name,
            "REQ: Delete Deviation Computed Orders",
            Action::DeleteDeviationComputedOrders,
        )
        .add_metadata("user_role", &tenant.role.to_string())
        .add_metadata("deviation_id", &payload.id)
        .add_metadata("seq", &seq),
    );

    let deviation_session = DeviationSession::build(payload.id.clone(), state.clone());

    match deviation_session.delete_order(&payload.seq).await {
        Ok(_) => {
            let response = serde_json::json!({
                "status": "success",
                "message": format!("Successfully Deleted the order"),
            });

            return Ok((StatusCode::ACCEPTED, Json(response)));
        }
        Err(err) => {
            let error_response = serde_json::json!({
                "status": "error",
                "message": format!("Failed to Delete Order"),
            });

            return Err((StatusCode::INTERNAL_SERVER_ERROR, Json(error_response)));
        }
    };
}
