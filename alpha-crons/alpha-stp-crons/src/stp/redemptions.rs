use super::{find_next_working_day, is_holiday};
use actlogica_logs::{builder::LogBuilder, log_error, log_info, log_warn};
use alpha_core_db::{
    schema::{
        client_order_entry::OrderStatus,
        portfolio::{portfolio_service::PortfolioServiceSTP, Portfolio},
        stp::{deployment_tracker::DeploymentTracker, systemetic_deployment::SystematicDeployment},
    },
    transactions::{commit_transaction, start_transaction},
};
use alpha_oms::oms::{save_orders, OrderComputer};
use chrono::{NaiveDate, Utc};

pub async fn trigger_redemption_for_today() -> Result<(), String> {
    let app_state = alpha_oms::models::AppState::new().await;
    log_info(LogBuilder::system(&format!(
        "Triggered Redemptions for: {}",
        Utc::today().naive_local()
    )));
    let pool = app_state.db.clone();
    let mut conn = pool.get().await.unwrap();

    // get all Active redemptions
    let mut active_redemptions = match SystematicDeployment::get_all_active_redemptions_for_today(&mut conn).await {
        Ok(redemptions) => redemptions,
        Err(err) => {
            log_error(LogBuilder::system(&format!(
                "Failed to get active redemptions: {}",
                err
            )));
            return Err(String::from("Failed to get active redemptions"));
        }
    };

    if active_redemptions.is_empty() {
        log_info(LogBuilder::system("No Active Redemptions Found"));
        return Ok(());
    };

    // Loop through all the active redemptions
    for (idx, deployment) in active_redemptions.iter_mut().enumerate() {
        log_info(LogBuilder::system(&format!(
            "Performing Redemption for Systematic  Deployment _id: {}",
            deployment.id
        )));

        // Check if the redemption date is a holiday
        if is_holiday(deployment.redemption_date).await.unwrap() {
            log_warn(LogBuilder::system(&format!(
                "Skipping Redemption for {}, as it is a holiday",
                deployment.id
            )));

            // Find next working day for redemption date
            let adjusted_redemption_date = find_next_working_day(deployment.redemption_date).await?;
            deployment.redemption_date = adjusted_redemption_date;

            // Calculate trigger period and find next working day for installment
            let trigger_period_in_days = chrono::Days::new(deployment.sell_trigger_period as u64);
            let tentative_installment_date = deployment
                .redemption_date
                .checked_add_days(trigger_period_in_days)
                .unwrap();
            deployment.instalment_date = find_next_working_day(tentative_installment_date).await?;

            log_info(LogBuilder::system(&format!(
                "Setting next redemption date to {} and \nnext installment date to {} for redemption_id: {}",
                deployment.redemption_date, deployment.instalment_date, deployment.id
            )));

            // update the redemption date & installment date
            deployment.update().await;
            if let Err(err) = deployment.commit(&mut conn).await {
                log_error(LogBuilder::system(&format!(
                    "Failed to update redemption date for {}",
                    &deployment.id
                )));
            }

            // skip the rest of the loop
            continue;
        }

        // begin the transaction
        start_transaction(&mut conn).await?;

        // post transaction actions
        let auto_sell_enabled = deployment.is_auto_sell_trigger;

        // check if there's any pending installments
        if deployment.pending_installments == 0 {
            log_warn(LogBuilder::system(&format!(
                "Skipping: Redemption for {} as there are no pending installments.",
                deployment.id
            )));
            continue;
        }

        // get installment amount
        let amount_to_redeem = deployment.instalment_amount;

        // get the source portfolio and check if there's enough cash
        let mut portfolio = PortfolioServiceSTP::get_portfolio_by_id_for_stp(
            &mut conn,
            deployment.source_portfolio_id.clone().unwrap(),
        )
        .await
        .unwrap();
        let portfolio_cash_position = Portfolio::get_portfolio_cash_position_by_id(&mut conn, portfolio.id.to_string())
            .await
            .unwrap()
            .unwrap();

        // find the next redemption date
        let next_redemption_date = find_next_redemption_date(&deployment).await?;

        // update the new redemption_date
        deployment.redemption_date = next_redemption_date.into();

        let mut allowed_to_generate_order = auto_sell_enabled;

        //Get the Previous Buy
        //If it is null means this is the first sell Happening for the Deployment (Need to change)
        if deployment.last_buy_tracker_id.is_some() {
            // Get the previous buy tracker
            let previous_installment_info =
                DeploymentTracker::get_by_id(&mut conn, deployment.last_buy_tracker_id.as_ref().unwrap()).await?;

            // Invalid tracker id
            if previous_installment_info.is_none() {
                continue;
            }

            // Check if the previous order is settled or partially settled
            let previous_installment_status = previous_installment_info.unwrap().status; // safe
            if previous_installment_status == OrderStatus::Settled.to_string()
                || previous_installment_status == OrderStatus::PartiallySettled.to_string()
            {
                allowed_to_generate_order = auto_sell_enabled;
            } else {
                log_warn(LogBuilder::system(&format!(
                    "Previous installment for {} is not settled. Skipping redemption",
                    deployment.id
                )));
                allowed_to_generate_order = false;
            }
        }

        if portfolio.market_value + portfolio_cash_position.current_cash_balance < amount_to_redeem {
            allowed_to_generate_order = false;
        }

        let rationale = format!(
            "{}Installment No - {}/{}",
            deployment.r#type,
            deployment.completed_installments + 1,
            deployment.no_of_installments
        );

        let tracker = DeploymentTracker {
            deployment_setup_id: deployment.id.clone(),
            installment_no: deployment.completed_installments + 1,
            status: if allowed_to_generate_order {
                OrderStatus::Draft.to_string()
            } else {
                "Created".to_string()
            },
            last_updated_date: Utc::now().naive_local(),
            setup_type: "Sell".to_string(),
            trigger_date: Utc::now().naive_local(),
            triggered_by: if allowed_to_generate_order {
                String::from("System")
            } else {
                String::from("Yet to be trigger")
            },
            rationale: rationale.clone(),
            deployment_type: "Systematic".to_string(),
            previous_tracker_id: deployment.last_buy_tracker_id.clone(),
        };
        let tracker_inserted_id = tracker
            .commit(&mut conn)
            .await
            .map_err(|e| {
                log_error(
                    LogBuilder::system("Failed to insert new Deployment Tracker to DB")
                        .add_metadata("error", &e.to_string()),
                );
            })
            .unwrap();

        //If auto sell trigger is true and previous Cycle order has been generated
        // Generate The order for this
        if allowed_to_generate_order {
            // create orderComputer here

            let order_computer = OrderComputer::new(
                app_state.db.clone(),
                app_state.master_db.clone(),
                app_state.tenant_redis_url.clone(),
                app_state.master_redis_url.clone(),
                "System".to_string(),
                amount_to_redeem,
            );
            let prepared_order_for_withdraw = order_computer
                .compute_order_for_capital_withdrawal_stp(portfolio.id.to_string(), tracker_inserted_id.clone())
                .await
                .unwrap();

            let save_orders = save_orders(&mut conn, prepared_order_for_withdraw.1, "STP".to_string())
                .await
                .unwrap();
        }
        deployment.last_sell_tracker_id = Some(tracker_inserted_id);
        deployment.last_buy_tracker_id = None;

        // update the Deployment with new dates
        if let Err(err) = deployment.commit(&mut conn).await {
            log_error(LogBuilder::system(&format!(
                "Failed to update redemption date for {}",
                &deployment.id
            )));
        };

        // commit transaction
        log_info(LogBuilder::system(&format!(
            "Commiting Transaction for Redemption for {}",
            deployment.id
        )));
        commit_transaction(&mut conn).await?;
    }
    Ok(())
}

// returns next redemption date
pub async fn find_next_redemption_date(deployment: &SystematicDeployment) -> Result<NaiveDate, String> {
    let prev_installment_date = deployment.redemption_date;
    let frequency = &deployment.frequency;

    let days_count = match frequency.as_str() {
        "Weekly" => chrono::Days::new(7),
        "Monthly" => chrono::Days::new(30),
        _ => chrono::Days::new(14), // Bi-weekly default
    };

    let next_calculated_date = prev_installment_date.checked_add_days(days_count).unwrap();

    let next_working_date = find_next_working_day(next_calculated_date).await?;
    log_info(LogBuilder::system(&format!(
        "Next Redemption Date for {} is {}",
        deployment.id, next_working_date
    )));

    return Ok(next_working_date.into());
}

#[cfg(test)]
mod tests {
    use crate::stp::redemptions::trigger_redemption_for_today;
    use actlogica_logs::setting::LogOutput;
    use actlogica_logs::{builder::LogBuilder, log_error, log_info, setting::init_logger};
    use tracing_subscriber::filter::LevelFilter;

    #[tokio::test]
    async fn test_trigger_redemption_for_today() {
        dotenv::dotenv().ok();
        if let Err(err) = init_logger("Testing-Redemption", LevelFilter::INFO, LogOutput::StdOut).await {
            log_error(LogBuilder::system("Failed to initialize logger").add_metadata("error", &err.to_string()));
        }
        log_info(LogBuilder::system("Logger service initialized in TestingRedemption"));

        let result = trigger_redemption_for_today().await;
        // assert_eq!(result.is_ok(), true);
    }
}
