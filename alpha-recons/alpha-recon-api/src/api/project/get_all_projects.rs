use crate::models::{AppState, recon_project::ReconProjectRsp};
use alpha_core_db::tenant_clickhouse::reconciliation::project::{AllProjectResponse, ReconProject};
use axum::{Json, extract::State, http::StatusCode, response::IntoResponse};
use serde::Serialize;
use std::sync::Arc;
use utoipa::ToSchema;

#[derive(Serialize, ToSchema)]
pub struct GetAllProjectRsp {
    projects: Vec<ReconProjectRsp>,
}

#[utoipa::path(
    get,
    tag = "Project",
    path = "/project/get-all",
    responses(
        (status = 200, description = "Projects found", body = GetAllProjectRsp),
        (status = 404, description = "Projects not found"),
        (status = 500, description = "Internal server error")
    )
)]
pub async fn get_all_projects(
    // Extension(tenant): Extension<TokenClaims>,
    State(state): State<Arc<AppState>>,
) -> Result<Json<GetAllProjectRsp>, impl IntoResponse> {
    let user_id = "test"; // TODO: get user id from token
    let projects = ReconProject::get_all(&state.tenant_clickhouse, &user_id).await;
    match projects {
        Ok(projects) => Ok(Json(GetAllProjectRsp {
            projects: projects.into_iter().map(Into::into).collect(),
        })),
        Err(e) => Err((StatusCode::INTERNAL_SERVER_ERROR, format!("Database Error: {}", e)).into_response()),
    }
}
